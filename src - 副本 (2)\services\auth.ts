/**
 * 认证相关服务函数
 */

/**
 * 用户登录函数
 * @param {string} username - 用户名
 * @param {string} password - 密码
 * @param {Function} setLoginConfirmLoading - 设置登录按钮加载状态的函数
 * @param {Function} setLoginState - 设置登录状态的函数
 * @returns {Promise} - 登录请求的Promise
 */
export const login = async (username, password, setLoginConfirmLoading, setLoginState, setLoginOpen = null) => {
    setLoginConfirmLoading(true); // 用于渲染 Button 是否加载中，转圈圈
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒后超时
    try {
        const response = await fetch("http://127.0.0.1:5000/user/login", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                'Authorization': 'Bearer ' + sessionStorage.getItem("access_token"),
            },
            body: JSON.stringify({username, password}),
            signal: controller.signal,
        });
        clearTimeout(timeoutId);
        const data = await response.json();
        console.log("收到登录数据：", data);
        if (response.ok) {
            console.log("登录成功");
            setLoginState(true)
            sessionStorage.setItem("access_token", data.access_token); // 存储 token
            sessionStorage.setItem("username", data.username); // 存储用户名
            sessionStorage.setItem("role", data.role); // 存储角色
            setLoginOpen(false); // 关闭弹窗
            return {success: true, data};
        } else {
            console.error(data.message || "登录失败");
            return {success: false, message: data.message || "登录失败"};
        }
    } catch (error) {
        if (error.name === "AbortError") {
            console.error("登录请求超时");
            return {success: false, message: "登录请求超时"};
        } else {
            console.error("登录请求失败:", error);
            return {success: false, message: error.message || "登录请求失败"};
        }
    } finally {
        setLoginConfirmLoading(false); // ✅ 确保 loading 状态结束
    }
};

export const register = async (setLoginState, setRegisterConfirmLoading, setRegisterOpen) => {
    setRegisterConfirmLoading(true);
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒后超时
    try {
        const response = await fetch("http://127.0.0.1:5000/user/register", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "Authorization": "Bearer " + sessionStorage.getItem("access_token")
            },
            signal: controller.signal,
        });
        clearTimeout(timeoutId);
        const data = await response.json();
        console.log("收到登录数据：", data);
        if(response.ok){
            console.log("注册成功");
            setLoginState(true);
            sessionStorage.setItem("access_token", data.access_token);
            sessionStorage.setItem("username", data.username);
            sessionStorage.setItem("role", data.role);
            setRegisterOpen(false); // 关闭弹窗
            return {success: true, data};
        } else {
            console.error(data.message || "注册失败");
            return {success: false, message: data.message || "注册失败"};
        }
    } catch (error) {
        if (error.name === "AbortError") {
            console.error("注册请求超时");
            return {success: false, message: "请求超时"};
        } else {
            console.error("注册请求失败:", error);
            return {success: false, message: error.message || "注册请求失败"};
        }
    } finally {
        setRegisterConfirmLoading(false);
    }
}

export const changePassword = async (username, password, setChangePasswordLoading, setChangePasswordOpen, setLoginState) => {
    setChangePasswordLoading(true);
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒后超时
    try {
        const response = await fetch("http://127.0.0.1:5000/user/change_password", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                'Authorization': 'Bearer ' + sessionStorage.getItem("access_token"),
            },
            body: JSON.stringify({ username, password }),
            signal: controller.signal
        });
        clearTimeout(timeoutId);
        const data = await response.json();

        if (response.ok) {
            console.log("密码修改成功");
            await logOut(setLoginState);
            setChangePasswordOpen(false); // 关闭弹窗
            return {success: true, data};
        } else {
            console.error(data.message || "改密失败");
            return {success: false, message: data.message || "改密失败"};
        }
    } catch (error) {
        if (error.name === "AbortError") {
            console.error("改密请求超时");
            return {success: false, message: "改密请求超时"};
        } else {
            console.error("改密请求失败:", error);
            return {success: false, message: error.message || "改密请求失败"};
        }
    }
    setChangePasswordLoading(false);
}
export const logOut = async (setLoginState) => {
    sessionStorage.clear();  // 清空所有 sessionStorage 数据
    setLoginState(false)
    // 强制更新 UI
    // window.location.reload();
}