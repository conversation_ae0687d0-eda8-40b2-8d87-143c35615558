# Product Overview

This is a radar data analysis and visualization application built with React. The application provides:

- **Radar Information Management**: View and manage radar device information and status
- **Scene Parameter Configuration**: Configure radar scene parameters and settings  
- **Data Analysis Dashboard**: Analyze radar data with real-time and historical views
- **3D Visualization**: Cesium-based 3D mapping and radar coordinate visualization
- **Authentication System**: Session-based login with radar access control

The application is designed for radar operators and analysts to monitor radar systems, configure parameters, and analyze collected data through an intuitive web interface with both 2D charts and 3D geographic visualization.

## Key Features
- Real-time radar status monitoring
- Multi-radar scene management
- Data visualization with Ant Design Charts
- 3D geographic mapping with Cesium
- Session-based authentication
- Responsive dashboard interface