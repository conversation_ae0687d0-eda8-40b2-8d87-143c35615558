import {useState} from "react";
import {openNotification} from "../notification";
import {HomeHead} from "./HomeHead.jsx";
import {Layout} from "antd";
const {Content, Footer} = Layout;

export function RadarInformation({ setRadarID, radarID, radarList, setRadarList, radarState, setRadarState, loginState, setLoginState, setSceneCoordinates, radarInformation, setRadarInformation, sceneName, setSceneName }) {
    const [loadings, setLoadings] = useState(false);
    const [loginOpenFlag, setLoginOpenFlag] = useState(false); //登录提示弹窗
    
    // // 如果sceneName为null或undefined，则从sessionStorage中获取
    // const displaySceneName = sceneName || sessionStorage.getItem('sceneName');
    //
    // // 如果从sessionStorage中获取到了sceneName，但props中的sceneName为null，则更新props中的sceneName
    // if (!sceneName && displaySceneName && setSceneName) {
    //     setSceneName(displaySceneName);
    // }

    const enterLoading = async () => {
        if (!loginState) {
            setLoginState(true)
        } else if (!radarID) {
            openNotification({
                type: "warning",
                mess: "请先选择雷达"
            })
        } else {
            try {
                setLoadings(true); // 开始加载
                const response = await fetch("http://127.0.0.1:5000/radar_information/update_radar_information", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "Authorization": "Bearer " + sessionStorage.getItem("access_token"),
                    },
                    body: JSON.stringify({radar_ID: radarID})
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    console.error("获取雷达信息失败:", errorData);
                    openNotification({type: "error", mess: `获取雷达信息失败: ${errorData.msg || '未知错误'}`});
                    return;
                }

                const data = await response.json();
                const generatedItems = data.data.map(item => ({
                    key: item.key,
                    label: item.label,
                    children: item.value ?? "--", // 使用服务器返回的值
                }));
                setRadarInformation(generatedItems);
                console.log("雷达信息请求成功:", data);
                openNotification({
                    type: data.status,
                    mess: data.message
                });
            } catch (error) {
                console.error("雷达信息请求失败:", error);
                openNotification({
                    type: "error",
                    mess: "雷达信息请求失败"
                });
            } finally {
                setTimeout(() => {
                    setLoadings(false)
                }, 5000);
            }
        }
    }
    console.log("场景---:", sceneName)
    return (
        <Layout>
            <HomeHead
                setRadarID={setRadarID}
                radarID={radarID}
                radarList={radarList}
                setRadarList={setRadarList}
                radarState={radarState}
                setRadarState={setRadarState}
                loginState={loginState}
                setLoginState={setLoginState}
                setSceneCoordinates={setSceneCoordinates}
                sceneName={sceneName}
                activeNumber="1"
            />
            <Content>ccc</Content>
            <Footer>qqqq</Footer>
        </Layout>
    )

}