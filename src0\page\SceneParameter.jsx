import { Bad<PERSON>, But<PERSON>, Col, Divider, Form, Input, InputNumber, Row, Select, Slider, Typography } from "antd";
import { useRef, useState } from "react";
import { NotLoggedIn, OpenNotification } from "../notification.jsx";

export function SceneParameter({
    radarID,
    loginState,
    form,
}) {

    const [loading, setLoading] = useState(false);
    const [updatePara, setUpdatePara] = useState(false);//判断是否查询参数

    const [psValue, setPsValue] = useState(0);
    const psOnChange = (newValue) => {
        setPsValue(newValue);
    };

    // 更新场景参数
    const handleSubmit = async (values) => {
        if (!radarID) {
            OpenNotification({
                type: "warning",
                mess: "请先选择雷达"
            })
        } else if (!updatePara) {
            OpenNotification({
                type: "warning",
                mess: "请先查询参数后再更新参数"
            })
        } else {
            // 确保所有数值字段都是数字类型而不是字符串
            const processedValues = {};
            for (const key in values) {
                // 检查值是否为字符串且可以转换为数字
                if (typeof values[key] === 'string' && !isNaN(Number(values[key]))) {
                    processedValues[key] = Number(values[key]);
                } else {
                    processedValues[key] = values[key];
                }
            }

            const dataToSend = {
                ...processedValues, // 处理后的表单数据
                radar_ID: radarID // 附上ID
            };
            // console.log("提交的表单数据:", dataToSend);
            try {
                const response = await fetch("http://127.0.0.1:5000/scene_parameter/update_scene_parameter", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "Authorization": "Bearer " + sessionStorage.getItem("access_token")
                    },
                    body: JSON.stringify(dataToSend)
                });

                const responseData = await response.json();
                const { status, message } = responseData;
                OpenNotification({
                    type: status,
                    mess: message
                })
                // response.ok ? console.log("表单提交成功") : console.log("表达提交失败");
            } catch (error) {
                console.log("请求失败:", error);
                OpenNotification({
                    type: "error",
                    mess: "发生错误"
                })
            }
        }
    };

    // 查询场景参数
    const queryData = async () => {
        if (!radarID) {
            OpenNotification({
                type: "warning",
                mess: "请先选择雷达"
            })
        } else {
            setLoading(true);
            try {
                const response = await fetch("http://127.0.0.1:5000/scene_parameter/check_scene_parameters", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "Authorization": "Bearer " + sessionStorage.getItem("access_token"),
                    },
                    body: JSON.stringify({ radar_ID: radarID })  // 发送表单的 ID
                });
                const responseData = await response.json();
                const { status, message, data } = responseData;
                // console.log("获取的服务器数据:", responseData);
                if (response.ok) {
                    form.setFieldsValue(data);
                    if (status === "success") {
                        setUpdatePara(true);
                    }
                }
                OpenNotification({
                    type: status,
                    mess: message
                })
            } catch (error) {
                // console.error("获取数据失败:", error);
                OpenNotification({
                    type: "error",
                    mess: "场景参数查询失败" + error
                });
            }
            setLoading(false)
        }
    }
    return (
        <Form form={form}
            layout={"vertical"}
            initialValues={{ requiredMarkValue: "optional" }}
            onFinish={handleSubmit}
        >
            <Row justify={"center"}>
                <Col span={24}>
                    <Row justify={"center"} gutter={[25]}>
                        <Col span={12}>
                            <Divider orientation={"center"} size={50} style={{ fontSize: 20, fontFamily: "cursive" }}>扫描参数</Divider>
                            <Row justify={"center"} gutter={[25]}>
                                <Col span={10}>
                                    <Form.Item label={"正扫转台转速"} name={"RoRate"}>
                                        <Select defaultValue={"停止"}
                                            options={[
                                                { value: 0, label: '停止' },
                                                { value: 20, label: '2rpm' },
                                                { value: 30, label: '1rpm' },
                                                { value: 50, label: '1/4rpm' },
                                                { value: 60, label: '1/16rpm' },
                                            ]}
                                        />
                                    </Form.Item>
                                    <Form.Item label={"转台起始扫描角"}
                                        tooltip={"从转台零位开始，顺时针方向，按最小步进量化"}
                                        name={"RotAngBgn"}
                                    >
                                        <Input placeholder="dd.dd°" suffix="°" />
                                    </Form.Item>
                                    <Form.Item label={"转台结束扫描角"}
                                        tooltip={"从转台零位开始，顺时针方向，按最小步进量化"}
                                        name={"RotAngEnd"}
                                    >
                                        <Input placeholder="dd.dd°" suffix="°" />
                                    </Form.Item>
                                </Col>
                                <Col span={10}>
                                    <Form.Item label="回波最近距离" name="RangeMin">
                                        <Input placeholder="00.00" suffix="m" />
                                    </Form.Item>
                                    <Form.Item label="回波最远距离" name="RangeMax">
                                        <Input placeholder="00.00" suffix="m" />
                                    </Form.Item>
                                    <Form.Item label="高分、性能模式切换" tooltip="性能模式占用整数低字节"
                                        name="RadarMode">
                                        <Select defaultValue={0}
                                            options={[
                                                { value: 0, label: '高分模式' },
                                                { value: 1, label: '性能模式' }
                                            ]}
                                        />
                                    </Form.Item>
                                </Col>
                            </Row>
                            <Row justify={"center"}>
                                <Form.Item label="PS筛选门限" tooltip="数值越大，PS点越多" style={{ width: '80%' }}
                                    name="PS_TD">
                                    <Row gutter={[25]}>
                                        <Col span={20}>
                                            <Slider
                                                min={0}
                                                max={40}
                                                value={psValue}
                                                onChange={psOnChange}
                                            />
                                        </Col>
                                        <Col span={4}>
                                            <InputNumber
                                                min={0}
                                                max={40}
                                                value={psValue}
                                                onChange={psOnChange}
                                            />
                                        </Col>
                                    </Row>
                                </Form.Item>
                            </Row>
                        </Col>
                        <Col span={12}>
                            <Divider orientation={"center"} size={50} style={{ fontSize: 20, fontFamily: "cursive" }}>输出参数</Divider>
                            <Row justify={"center"} gutter={[25]}>
                                <Col span={10}>
                                    <Form.Item label="起点停留时间" name="StartStopTime">
                                        <Input placeholder="00.00" suffix="s" />
                                    </Form.Item>
                                    <Form.Item label="散射图像有效" name="ScatImageEn">
                                        <Select defaultValue={0}
                                            options={[
                                                { label: '无效', value: 0 },
                                                { label: '有效', value: 1 }
                                            ]}
                                        />
                                    </Form.Item>
                                    <Form.Item label="形变图像有效" name="DefoImageEn">
                                        <Select defaultValue={0}
                                            options={[
                                                { label: '无效', value: 0 },
                                                { label: '有效', value: 1 }
                                            ]}
                                        />
                                    </Form.Item>
                                    <Form.Item label="上位机ID启用开关" name="MissionIDSwitch">
                                        <Select defaultValue={0}
                                            options={[
                                                { label: '雷达自己生成任务ID', value: 0 },
                                                { label: '使用上位机设置任务ID', value: 1 }
                                            ]}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col span={10}>
                                    <Form.Item label="终点停留时间" name="EndStopTime">
                                        <Input placeholder="00.00" suffix="s" />
                                    </Form.Item>
                                    <Form.Item label="散射图像帧抽取" tooltip="指隔多少帧输出一副散射图"
                                        name="ScatImageDec">
                                        <Input placeholder="0" suffix="帧" />
                                    </Form.Item>
                                    <Form.Item label="形变图像帧抽取" tooltip="指隔多少帧输出一副形变图"
                                        name="DefoImageDec">
                                        <Input placeholder="0" suffix="帧" />
                                    </Form.Item>
                                    <Form.Item label="任务ID" tooltip="任务ID，区分不同扫描任务" name="MissionID">
                                        <Input placeholder="target id" />
                                    </Form.Item>
                                </Col>
                            </Row>
                        </Col>
                    </Row>
                    {/*==============上下分界线======================================*/}
                    <Row justify={"center"}>
                        <Col span={24}>
                            <Divider orientation={"center"} size={50} style={{ fontSize: 20, fontFamily: "cursive" }}>其他参数</Divider>
                            <Row justify={"space-around"} gutter={[20, 20]}>
                                <Col span={10}>
                                    <Form.Item label="天线方位波束角" tooltip="-6dB波束宽度" name="AntBWAz">
                                        <Input placeholder="°" suffix="°" />
                                    </Form.Item>
                                    <Form.Item label="天线舵机俯仰" tooltip="单位：0.1°，实际角度乘10取整，例如1度以10表示"
                                        name="AntSteerVt">
                                        <Input placeholder="" suffix="°" />
                                    </Form.Item>
                                    <Form.Item label="雷达位置经度" tooltip="正表示东经，负表示西经" name="RadarLon">
                                        <Input placeholder="dd.dddddd°" suffix="°" />
                                    </Form.Item>
                                    <Form.Item label="雷达位置维度" tooltip="正表示北纬，负表示南纬" name="RadarLat">
                                        <Input placeholder="dd.dddddd°" suffix="°" />
                                    </Form.Item>
                                    <Form.Item label="定位模式" name="LocaltionType">
                                        <Select defaultValue={0}
                                            options={[
                                                { label: "手动定位", value: 0 },
                                                { label: "自动定位", value: 1 }
                                            ]}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col span={10}>
                                    <Form.Item label="雷达位置高度" name="RadarHei">
                                        <Input placeholder="00." suffix="m" />
                                    </Form.Item>
                                    <Form.Item label="雷达转台零点朝向" tooltip="相对于正北方向，顺时针" name="RadarOri">
                                        <Input placeholder="dd.dd°" suffix="°" />
                                    </Form.Item>
                                    <Form.Item label="雷达转臂长度" tooltip="单位：米 转轴到天线相位中心距离"
                                        name="RadarArmLen">
                                        <Input placeholder="00.00" suffix="m" />
                                    </Form.Item>
                                    <Form.Item label="滤波控制" tooltip="This is a required field" name="FilterType">
                                        <Input placeholder="input placeholder" />
                                    </Form.Item>
                                </Col>
                            </Row>
                            <Row justify={"center"}>
                                <Button type={"primary"} htmlType={"submit"}>
                                    提交表单
                                </Button>
                                <Col span={1}></Col>
                                <Button
                                    loading={loading}
                                    type={"primary"}
                                    onClick={() => queryData()}
                                >
                                    查询参数[更新数据库中的MissionID]
                                </Button>
                            </Row>
                        </Col>
                    </Row>
                </Col>
            </Row>
        </Form>
    )
}