# Project Structure

## Root Directory
```
├── src/                    # Main source code
├── public/                 # Static assets (vite.svg)
├── static/images/          # Additional static images
├── node_modules/           # Dependencies
├── .kiro/                  # Kiro AI assistant configuration
├── .idea/                  # IDE configuration
├── package.json            # Project dependencies and scripts
├── vite.config.js          # Vite build configuration
├── eslint.config.js        # ESLint configuration
└── index.html              # Entry HTML file
```

## Source Code Organization (`src/`)
```
src/
├── main.jsx               # Application entry point
├── App.jsx                # Main app component with routing
├── index.css              # Global styles
├── notification.jsx       # Notification component
├── page/                  # Page components (views)
│   ├── CesiumViewer.ts    # Cesium 3D viewer logic
│   ├── DataAnalysis.jsx   # Data analysis dashboard
│   ├── HomeHead.jsx       # Header component
│   ├── PrivateRoute.jsx   # Route protection
│   ├── RadarInformation.jsx # Radar info management
│   ├── SceneParameter.jsx # Scene configuration
│   └── TestLogin.jsx      # Authentication page
├── services/              # API and service layer
│   └── auth.ts            # Authentication services
└── assets/                # Static assets (images, icons)
```

## Architecture Patterns

### Routing Structure
- `/test_login` - Authentication and scene selection
- `/` - Protected routes (requires login)
  - `/RadarInformation` - Radar device management
  - `/about` - Scene parameter configuration  
  - `/data_analysis` - Main data analysis dashboard

### State Management
- Uses React hooks and local state
- Session storage for persistence (login state, radar selection, scene data)
- Form instances managed with Ant Design Form hooks
- Shared state passed through props between components

### Component Organization
- **Pages**: Main route components in `src/page/`
- **Services**: API calls and business logic in `src/services/`
- **Assets**: Static resources in `src/assets/`
- **Styles**: Component-specific CSS files alongside JSX files

## Naming Conventions
- React components use PascalCase (e.g., `DataAnalysis.jsx`)
- Service files use camelCase (e.g., `auth.ts`)
- CSS files match component names (e.g., `App.css` for `App.jsx`)
- Mixed file extensions: `.jsx` for React components, `.ts` for TypeScript utilities

## Legacy Code
- Multiple `src - 副本` folders exist (backup copies) - avoid modifying these
- Focus development on the main `src/` directory