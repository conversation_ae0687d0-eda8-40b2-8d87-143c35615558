import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Dropdown, Flex, Input, Layout, Menu, Modal, Select, Space } from "antd";
import { <PERSON> } from "react-router-dom";
import { UserOutlined } from "@ant-design/icons"
import { useEffect, useState } from "react";
import {
    changePassword as authChangePassword,
    login as authLogin,
    logOut,
    register as authRegister
} from "../services/auth.js";

const { Header } = Layout;

export function HomeHead({
    radarList,
    setRadarList,
    setRadarID,
    radarState,
    setRadarState,
    radarID,
    loginState,
    setLoginState,
    setSceneCoordinates,
    sceneName,
    activeNumber
}) {
    // console.log("HomeHead记录的登录状态:", loginState);
    // console.log("HomeHead记录的雷达列表:", radarList);

    const listenRadarState = async (radarID) => {
        try {
            const response = await fetch("http://127.0.0.1:5000/listen_radar_state", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    'Authorization': 'Bearer ' + sessionStorage.getItem("access_token"),
                },
                body: JSON.stringify({ radar_ID: radarID }),  // 发送表单的 ID
            });
            const { status, message, data } = await response.json();
            // openNotification({type: status, mess: message});
            if (response.ok) {
                const value = {
                    color: data.status === "offline" ? "#ff0000" : data.status === "work" ? "#fff500" : "#04f618",
                    text: data.status === "offline" ? "离线" : data.status === "work" ? "工作中" : "在线",
                }
                // console.log(value)
                // 根据状态调整颜色
                setRadarState(value);
                sessionStorage.setItem("radarState", JSON.stringify(value));
            } else {
                const defaultValue = {
                    color: "#fa1c30",  // 默认颜色
                    text: "离线",  // 默认值
                }
                setRadarState(defaultValue)
                sessionStorage.setItem("radarState", JSON.stringify(defaultValue));
            }
        } catch (error) {
            console.error("获取数据失败:", error);
            const defaultValue = {
                color: "#fa1c30",  // 默认颜色
                text: "离线",  // 默认值
            }
            setRadarState(defaultValue)
            sessionStorage.setItem("radarState", JSON.stringify(defaultValue));
        }
    }
    useEffect(() => {
        if (!radarID) {
            return
        }
        const interval = setInterval(() => {
            // console.log("开始监听雷达状态")
            listenRadarState(radarID)
        }, 5000)
        return () => clearInterval(interval);
    }, [radarID]); // 依赖 radarID，ID 变化时重新启动定时任务
    return (
        <Header style={{ position: 'sticky', top: 0, zIndex: 999 }}>
            <Flex align={"center"} gap="middle">
                <span style={{ color: "white", fontFamily: "cursive", fontSize: 20 }}>{sceneName}</span>
                <Menu theme={"dark"}
                    mode="horizontal"
                    style={{ flex: 1, minWidth: 0 }}
                    defaultSelectedKeys={
                        [activeNumber]
                    }
                    items={[
                        { key: 1, label: <Link to="/RadarInformation">雷达信息</Link> },
                        { key: 2, label: <Link to="/about">成像参数</Link> },
                        { key: 3, label: <Link to="/data_analysis">数据分析</Link> },
                        { key: 4, label: <Link to="/radar_manage">雷达管理</Link> }
                    ]}
                />
                <Flex align={"baseline"} gap={"8px"}>
                    {
                        loginState === true && (<>
                            <span style={{ color: "white" }}>当前雷达：</span>
                            <Select
                                style={{ minWidth: "130px", width: "auto" }}
                                options={radarList}
                                value={radarID}
                                onChange={(value) => {
                                    setRadarID(value);
                                    sessionStorage.setItem("radar_id", value);
                                    // console.log("选择雷达: ", value);
                                }}
                            />
                            <Badge
                                count={"0"}
                                showZero
                                style={{ backgroundColor: radarState.color, color: radarState.color, marginRight: 8 }}
                            />
                            <span style={{ color: 'white' }}>{radarState.text}</span>
                        </>)
                    }
                    <UserLogin
                        loginState={loginState}
                        setLoginState={setLoginState}
                    />
                </Flex>
            </Flex>
        </Header>
    )
}

const UserLogin = ({ loginState, setLoginState }) => {
    const [loginOpen, setLoginOpen] = useState(false)
    const [loginConfirmLoading, setLoginConfirmLoading] = useState(false)
    const [registerOpen, setRegisterOpen] = useState(false)
    const [registerConfirmLoading, setRegisterConfirmLoading] = useState(false)
    const [changePasswordOpen, setChangePasswordOpen] = useState(false)
    const [changePasswordConfirmLoading, setChangePasswordLoading] = useState(false)
    const [username, setUsername] = useState("");
    const [password, setPassword] = useState("");

    const login = async () => {
        const result = await authLogin(username, password, setLoginConfirmLoading, setLoginState, setLoginOpen);
        if (!result.success) {
            // console.error(result.message || "登录失败");
        }
    }
    const register = async () => {
        const result = await authRegister(setLoginState, setRegisterConfirmLoading, setRegisterOpen);
        if (!result.success) {
            // console.error(result.message || "注册失败");
        }
    }

    const changePassword = async () => {
        const result = await authChangePassword(username, password, setChangePasswordLoading, setChangePasswordOpen, setLoginState);
        if (!result.success) {
            // console.error(result.message || "改密失败");
        }
    }

    return (
        <Space>
            <Dropdown
                overlayStyle={{ zIndex: 1000 }}
                placement={"bottomLeft"}
                arrow
                popupRender={() => (
                    <Card title={"Welcome"} onClick={(e) => e.stopPropagation()}>
                        <Space direction={"vertical"} align={"center"}>
                            <UserOutlined style={{ fontSize: '32px' }} />
                            <span>{loginState ? sessionStorage.getItem("username") : "未登录"}</span>
                            {
                                loginState === false && (
                                    <div>
                                        <Button onClick={() => setRegisterOpen(true)}>
                                            注册
                                        </Button>
                                        <Button onClick={() => setLoginOpen(true)}>
                                            登录
                                        </Button>
                                    </div>
                                )
                            }
                            {
                                loginState === true && (
                                    <div>
                                        <Button onClick={() => logOut(setLoginState)}>
                                            退出
                                        </Button>
                                        <Button onClick={() => setChangePasswordOpen(true)}>
                                            改密
                                        </Button>
                                    </div>
                                )
                            }
                            <Modal
                                title={"登录"}
                                open={loginOpen}
                                onOk={() => login()}
                                confirmLoading={loginConfirmLoading}
                                onCancel={() => setLoginOpen(false)}
                            >
                                <Input
                                    placeholder={"用户名"}
                                    value={username}
                                    onChange={(e) => setUsername(e.target.value)}
                                    style={{ marginBottom: 10 }}
                                />

                                <Input.Password
                                    placeholder={"密码"}
                                    value={password}
                                    onChange={(e) => setPassword(e.target.value)}
                                    style={{ marginBottom: 10 }}
                                />
                            </Modal>
                            <Modal
                                title={"注册"}
                                open={registerOpen}
                                onOk={() => register()}
                                confirmLoading={registerConfirmLoading}
                                onCancel={() => setRegisterOpen(false)}
                            >
                                <Input
                                    placeholder={"用户名"}
                                    value={username}
                                    onChange={(e) => setUsername(e.target.value)}
                                    style={{ marginBottom: 10 }}
                                />

                                <Input.Password
                                    placeholder={"密码"}
                                    value={password}
                                    onChange={(e) => setPassword(e.target.value)}
                                    style={{ marginBottom: 10 }}
                                />
                            </Modal>
                            <Modal
                                title={"修改密码"}
                                open={changePasswordOpen}
                                onOk={() => changePassword()}
                                confirmLoading={changePasswordConfirmLoading}
                                onCancel={() => setChangePasswordOpen(false)}
                            >
                                <Input
                                    disabled placeholder={sessionStorage.getItem("username")}
                                    // placeholder={"用户名"}
                                    // value={username}
                                    // onChange={(e) => setUsername(e.target.value)}
                                    style={{ marginBottom: 10 }}
                                />

                                <Input.Password
                                    placeholder={"密码"}
                                    value={password}
                                    onChange={(e) => setPassword(e.target.value)}
                                    style={{ marginBottom: 10 }}
                                />
                            </Modal>
                        </Space>
                    </Card>
                )}
            >
                <Button type={"text"} style={{ color: "white" }}>
                    {loginState ? sessionStorage.getItem("username") : "未登录"}
                </Button>
            </Dropdown>
        </Space>
    )
}