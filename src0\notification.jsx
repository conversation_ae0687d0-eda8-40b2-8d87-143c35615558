//提示未登录组件
import { useEffect } from "react";
import { notification } from "antd";

notification.config({
    top: 70, // 直接设置通知框距离顶部的距离
});

export function NotLoggedIn({ openFlag, setOpenFlag }) {
    const [api, contextHolder] = notification.useNotification();
    const openNotification = () => {
        if (openFlag) {
            api["info"]({
                message: "未登录提示",
                description: "请先登录后再进行操作。",
                placement: "topRight",
                showProgress: true,
            });
            setOpenFlag(false);
        }
    };
    useEffect(() => openNotification(), [openFlag]);

    return (
        <div>
            {contextHolder}
        </div>
    )
}

//弹窗通知
export function OpenNotification({ type, mess }) {
    notification[type]({
        message: type === 'success' ? '成功' : type === 'error' ? '错误' : type === 'warning' ? '警告' : '提示',
        description: mess,
        placement: "topRight",
        showProgress: true,
        pauseOnHover: false,
    });

}