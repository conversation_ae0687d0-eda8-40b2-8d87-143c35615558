import React, {useEffect, useRef, useState} from "react";
import {<PERSON>sium<PERSON>iewer} from "./CesiumViewer.js";
import {Button, Carousel, Col, Input} from "antd";
import {useNavigate} from "react-router-dom";
import {OpenNotification} from "../notification";
import { login as authLogin } from "../services/auth.ts";

export default function TestLogin({loginState, setLoginState, setSceneName, setRadarList, setSceneCoordinates}) {
    const [viewer, setViewer] = useState(null);
    const [sceneList, setSceneList] = useState([]);
    const navigate = useNavigate();
    const [username, setUsername] = useState(""); // 用户名
    const [password, setPassword] = useState(""); // 密码
    const [loginConfirmLoading, setLoginConfirmLoading] = useState(false); //登陆中状态显示

    const login = async () => {
        const result = await authLogin(username, password, setLoginConfirmLoading, setLoginState);
        if (!result.success) {
            console.error(result.message || "登录失败");
        }
    }

    const checkAllRadar = async () => {
        try {
            const response = await fetch("http://127.0.0.1:5000/check_all_radar", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    'Authorization': 'Bearer ' + sessionStorage.getItem("access_token"),
                },
                body: JSON.stringify({scene_ID: sessionStorage.getItem("sceneID")}),  // 发送表单的 ID
            });
            const data = await response.json();
            console.log("收到雷达列表：", data);
            const options = data.data.map((radar) => ({
                value: radar.ID,    // 这里假设服务器返回的数据包含 id
                label: radar.name,  // 这里假设服务器返回的数据包含 name
            }));
            sessionStorage.setItem("radar_list", JSON.stringify(options));
            setRadarList(options);
        } catch (error) {
            console.error("获取雷达列表失败:", error);
        }
    }

    const getSceneList = async () => {
        try {
            const username = sessionStorage.getItem("username");
            if (!username) {
                console.error("用户名不存在");
                OpenNotification({type: "error", mess: "用户名不存在，请重新登录"});
                return;
            }
            
            console.log("正在获取场景列表，用户名:", username);
            
            const response = await fetch("http://127.0.0.1:5000/get_scene_list", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    'Authorization': 'Bearer ' + sessionStorage.getItem("access_token"),
                },
                body: JSON.stringify({
                    user_name: username,
                    // 添加其他可能需要的参数
                }),
            });
            
            if (!response.ok) {
                const errorData = await response.json();
                console.error("场景列表请求失败:", errorData);
                OpenNotification({type: "error", mess: `场景列表请求失败: ${errorData.msg || '未知错误'}`});
                setSceneList([]);
                return;
            }
            
            const data = await response.json();
            console.log("收到场景列表数据:", data);
            
            if (data && data.data && Array.isArray(data.data)) {
                setSceneList(data.data);
                console.log("设置场景列表成功，数量:", data.data.length);
                console.log("场景列表为:", data.data)
                if (viewer && data.data.length > 0) {
                    viewer.rotateEnabled = false;
                    viewer.flyTo(data.data[0].coordinates, 3);
                }
            } else {
                console.error("场景列表数据格式不正确:", data);
                setSceneList([]);
                OpenNotification({type: "warning", mess: "没有可用的场景数据"});
            }
        } catch (error) {
            console.error("场景列表请求失败:", error);
            OpenNotification({type: "error", mess: "场景列表请求失败: " + (error.message || '未知错误')});
            setSceneList([]);
        }
    }

    const toSelect = async ({sceneID, sceneName, sceneCoordinates}) => {
        setSceneName(sceneName);
        sessionStorage.setItem("sceneID", sceneID);
        sessionStorage.setItem("sceneName", sceneName);
        await checkAllRadar();
        setSceneCoordinates(sceneCoordinates);
        console.log("选择的场景为:", sceneName);
        console.log("选择的场景坐标为：", sceneCoordinates);
        // 无需销毁，交由 useEffect 销毁即可
        // if (viewer) {
        //     viewer.destroy();
        // }
        navigate("/RadarInformation");
    }
    useEffect(() => {
        // 创建Cesium Viewer实例
        const cesiumViewer = new CesiumViewer("cesiumLogin");
        setViewer(cesiumViewer);
        
        if (loginState === false) {
            cesiumViewer.build_login();
            return;
        }
        console.log("获取场景列表");
        getSceneList();
        
        // 组件卸载时清理
        return () => {
            if (cesiumViewer) {
                cesiumViewer.destroy();
            }
        };
    }, [loginState]);

    const passwordRef = useRef(null);

    return (
        <div style={{position: "relative", width: "100vw", height: "100vh"}}>
             {/*全屏 Cesium*/}
            <div
                // ref={cesiumContainerRef}
                id="cesiumLogin"
                style={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    width: "100%",
                    height: "100%",
                    zIndex: 0,
                }}
            />
            {loginState === true ? (
                <div
                    style={{
                        position: "absolute",
                        top: "20%",
                        left: "5%",
                        zIndex: 1,
                        backgroundColor: "rgba(255, 255, 255, 0)",
                        padding: "2rem",
                        borderRadius: "10px",
                        // boxShadow: "0 0 10px rgba(0,0,0,0.2)",
                        width: "600px",           // 固定宽度
                        maxWidth: "40%",          // 最大不超过屏幕宽度的90%
                    }}
                >
                    {sceneList.length === 0 ? (
                        <div>暂无场景数据</div>
                    ) : (
                        <div style={{width: "80%", maxWidth: "800px"}}>
                            <Carousel
                                arrows
                                afterChange={(current) => {
                                    const currentScene = sceneList[current];
                                    if (viewer) {
                                        viewer.flyTo(currentScene.coordinates, 3);
                                    }
                                    // console.log("当前 scene ID:", currentScene?.coordinates);
                                }}
                            >
                                {sceneList.map((scene) => (
                                    <Col>
                                        <span
                                            style={{
                                                color: "white",
                                                fontSize: "1.5vw", // 设置字体大小
                                                fontWeight: "bold", // 字体加粗
                                                display: "block", // 让文字独占一行
                                                textAlign: "center", // 文字居中
                                                // padding: "10px 0", // 上下内边距
                                            }}
                                        >
                                            {`点击进入->${scene.name}`}
                                        </span>
                                        <img
                                            src={`http://127.0.0.1:5000/image/${scene.background_pack}`}
                                            alt="图1"
                                            onClick={() => toSelect({
                                                sceneID: scene.ID,
                                                sceneName: scene.name,
                                                sceneCoordinates: scene.coordinates
                                            })}
                                            style={{
                                                width: "100%",
                                                height: "100%",
                                                cursor: "pointer",
                                                transition: "transform 0.3s ease-in-out", // 平滑动画
                                            }}
                                            onMouseOver={e => e.currentTarget.style.transform = "scale(1.05)"} // 鼠标悬停放大
                                            onMouseOut={e => e.currentTarget.style.transform = "scale(1)"} // 鼠标移出恢复
                                        />
                                    </Col>
                                ))}
                            </Carousel>
                        </div>
                    )
                    }
                </div>
            ) : (
                <div
                    style={{
                        position: "absolute",
                        top: "30%",
                        left: "10%",
                        zIndex: 1,
                        backgroundColor: "rgba(255, 255, 255, 0)",
                        padding: "2rem",
                        borderRadius: "10px",
                        // boxShadow: "0 0 10px rgba(0,0,0,0.2)",
                        width: "300px",           // 固定宽度
                        maxWidth: "30%",          // 最大不超过屏幕宽度的90%
                    }}
                >
                    <h2 style={{
                        color: "white",
                    }}
                    >边坡形变监测系统</h2>
                    <Input
                        placeholder="用户名"
                        variant="underlined"
                        value={username}
                        autoFocus
                        onChange={(e) => setUsername(e.target.value)}
                        style={{
                            borderRadius: "5px",
                            marginBottom: 15,
                        }}
                        onPressEnter={() => passwordRef.current?.focus()}
                        // prefix = {<UserOutlined />}
                    />
                    <Input.Password
                        placeholder="密码"
                        variant="underlined"
                        value={password}
                        ref={passwordRef}
                        onChange={(e) => setPassword(e.target.value)}
                        style={{
                            borderRadius: "5px",
                            marginBottom: 15,
                        }}
                        onPressEnter={() => login()}
                    />
                    <Button
                        type="primary"
                        loading={loginConfirmLoading}
                        onClick={() => login()}
                    >
                        登录
                    </Button>
                </div>
            )}


        </div>

    );
}
