import React, {useEffect, useState} from 'react';
import {Form, Layout} from 'antd';
import {BrowserRouter, Route, Routes} from 'react-router-dom';
import '@ant-design/v5-patch-for-react-19';

import PrivateRoute from "./page/PrivateRoute";
import {RadarInformation} from "./page/RadarInformation";
import {SceneParameter} from "./page/SceneParameter"
import TestLogin from "./page/TestLogin";

const {Content, Footer} = Layout;


export default function App() {
    const [loginState, setLoginState] = useState(() => {
        return !!sessionStorage.getItem("access_token"); // 如果存在，则返回true，否则返回false
    });//判断是否登录
    const [sceneName, setSceneName] = useState(() => {
        return sessionStorage.getItem("sceneName");
    });//选择的场景ID
    const [formSceneParameter] = Form.useForm();  // 创建一个表单实例
    const [formCardShowData] = Form.useForm();  // 创建一个表单实例
    const [radarList, setRadarList] = useState(() => {
        return JSON.parse(sessionStorage.getItem("radar_list"));
    }); // 存储雷达列表
    const [radarID, setRadarID] = useState(() => {
        return sessionStorage.getItem("radar_id");
    });// 存储当前选中雷达
    const [missionID, setMissionID] = useState(null);// 存储任务列表
    const [showTimeSelected, setShowTimeSelected] = useState("0"); // 默认选中“实时图像”
    const [radarInformation, setRadarInformation] = useState([]);//储存雷达信息
    const [sceneCoordinates, setSceneCoordinates] = useState([]);
    const [radarState, setRadarState] = useState(() => {
        return sessionStorage.getItem("radarState") || {
            color: "#fa1c30",  // 默认颜色
            text: "未在线",  // 默认值
        };
    });
    // // 监听sceneName变化，更新sessionStorage
    // useEffect(() => {
    //     if (sceneName) {
    //         sessionStorage.setItem('sceneName', sceneName);
    //     } else {
    //         sessionStorage.removeItem('sceneName');
    //     }
    // }, [sceneName]);

    useEffect(() => {
        if (loginState) {
            return;
        }
        setSceneName(null);
        sessionStorage.removeItem('sceneName'); // 登出时清除sessionStorage中的sceneName
        formSceneParameter.resetFields();
        formCardShowData.resetFields();
        setRadarList([])
        setRadarID(null);
        setMissionID(null);
        setShowTimeSelected("0");
        setRadarInformation([]);
        setSceneCoordinates([]);
        setRadarState({
            text: "未在线",  // 默认值
            color: "#fa1c30",  // 默认颜色
        });
    }, [loginState]);
    useEffect(() => {
        console.log("登陆状态", loginState)
    }, []);
    return (
        <BrowserRouter>
            <Routes>
                <Route
                    path="/test_login" // 设定登录状态，在登录界面确定场景的名称、场景内部雷达、以及该场景的坐标
                    element={<TestLogin
                        loginState={loginState}
                        setLoginState={setLoginState}
                        setSceneName={setSceneName}
                        setRadarList={setRadarList}
                        setSceneCoordinates={setSceneCoordinates}
                    />}
                />
                <Route
                    path="/"
                    element={
                        <PrivateRoute
                            radarID={radarID}
                            setRadarID={setRadarID}
                            radarList={radarList}
                            setRadarList={setRadarList}
                            radarState={radarState}
                            setRadarState={setRadarState}
                            loginState={loginState}
                            setLoginState={setLoginState}
                            setSceneCoordinates={setSceneCoordinates}
                            sceneName={sceneName}
                        >
                        </PrivateRoute>
                    }
                >
                    <Route path="RadarInformation" element={<RadarInformation
                        setRadarID={setRadarID}
                        radarID={radarID}
                        radarList={radarList}
                        setRadarList={setRadarList} // 在TestLogin中使用过
                        radarState={radarState}
                        setRadarState={setRadarState}
                        loginState={loginState} // 在TestLogin中使用过
                        setLoginState={setLoginState} // 在TestLogin中使用过
                        setSceneCoordinates={setSceneCoordinates} // 在TestLogin中使用过
                        radarInformation={radarInformation}
                        setRadarInformation={setRadarInformation}
                        sceneName={sceneName}
                    />}/>
                    <Route path="about" element={<SceneParameter
                        radarID={radarID}
                        setRadarID={setRadarID}
                        radarList={radarList}
                        setRadarList={setRadarList}
                        radarState={radarState}
                        setRadarState={setRadarState}
                        loginState={loginState}
                        setLoginState={setLoginState}
                        setSceneCoordinates={setSceneCoordinates}
                        form={formSceneParameter}
                        sceneName={sceneName}
                    />} />
                </Route>
            </Routes>
        </BrowserRouter>
    );
}