# Technology Stack

## Build System & Framework
- **Vite**: Modern build tool with HMR (Hot Module Replacement)
- **React 19**: Latest React with StrictMode enabled
- **ES Modules**: Project uses `"type": "module"` configuration

## Core Dependencies
- **React Router DOM**: Client-side routing (`react-router-dom@^7.6.2`)
- **Ant Design**: UI component library (`antd@^5.26.0`)
- **Ant Design Charts**: Data visualization (`@ant-design/charts@^2.4.0`)
- **Cesium**: 3D globe and mapping (`cesium@^1.130.0`)
- **React 19 Patch**: Compatibility patch for Ant Design (`@ant-design/v5-patch-for-react-19`)

## Development Tools
- **ESLint**: Code linting with React-specific rules
- **Vite Plugin React**: Fast Refresh support
- **Vite Plugin Cesium**: Cesium integration for Vite

## Common Commands

### Development
```bash
npm run dev          # Start development server
npm run preview      # Preview production build locally
```

### Build & Deploy
```bash
npm run build        # Build for production
```

### Code Quality
```bash
npm run lint         # Run ESLint checks
```

## Configuration Notes
- Cesium requires special Vite plugin configuration for Worker files and static assets
- ESLint configured for React hooks and React Refresh
- Project ignores unused variables that start with uppercase (likely constants)
- Cesium CSS must be imported in main.jsx for proper styling