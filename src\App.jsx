import React from 'react';
import { Form } from 'antd';
import { BrowserRouter, Route, Routes } from 'react-router-dom';
import '@ant-design/v5-patch-for-react-19';

import PrivateRoute from "./page/PrivateRoute";
import { RadarInformation } from "./page/RadarInformation";
import { SceneParameter } from "./page/SceneParameter"
import { DataAnalysis } from "./page/DataAnalysis"
import TestLogin from "./page/TestLogin";
import { AppStateProvider, useAppState } from "./context/AppStateContext";

// 内部App组件，使用Context
function AppContent() {
    const {
        loginState,
        setLoginState,
        sceneName,
        setSceneName,
        radarList,
        setRadarList,
        radarID,
        setRadarID,
        missionID,
        setMissionID,
        showTimeSelected,
        setShowTimeSelected,
        radarInformation,
        setRadarInformation,
        sceneCoordinates,
        setSceneCoordinates,
        radarState,
        setRadarState
    } = useAppState();

    const [formSceneParameter] = Form.useForm();
    const [formCardShowData] = Form.useForm();

    return (
        <BrowserRouter>
            <Routes>
                <Route
                    path="/test_login"
                    element={<TestLogin
                        loginState={loginState}
                        setLoginState={setLoginState}
                        setSceneName={setSceneName}
                        setRadarList={setRadarList}
                        setSceneCoordinates={setSceneCoordinates}
                    />}
                />
                <Route
                    path="/"
                    element={
                        <PrivateRoute
                            radarID={radarID}
                            setRadarID={setRadarID}
                            radarList={radarList}
                            setRadarList={setRadarList}
                            radarState={radarState}
                            setRadarState={setRadarState}
                            loginState={loginState}
                            setLoginState={setLoginState}
                            setSceneCoordinates={setSceneCoordinates}
                            sceneName={sceneName}
                        >
                        </PrivateRoute>
                    }
                >
                    <Route path="RadarInformation" element={<RadarInformation
                        radarID={radarID}
                        loginState={loginState}
                        radarInformation={radarInformation}
                        setRadarInformation={setRadarInformation}
                        sceneName={sceneName}
                    />} />
                    <Route path="about" element={<SceneParameter
                        radarID={radarID}
                        loginState={loginState}
                        form={formSceneParameter}
                    />} />
                    <Route path="data_analysis" element={<DataAnalysis
                        setRadarID={setRadarID}
                        radarID={radarID}
                        radarList={radarList}
                        setRadarList={setRadarList}
                        radarState={radarState}
                        setRadarState={setRadarState}
                        loginState={loginState}
                        setLoginState={setLoginState}
                        missionID={missionID}
                        setMissionID={setMissionID}
                        formCardShowData={formCardShowData}
                        showTimeSelected={showTimeSelected}
                        setShowTimeSelected={setShowTimeSelected}
                        sceneCoordinates={sceneCoordinates}
                        setSceneCoordinates={setSceneCoordinates}
                        sceneName={sceneName}
                    />} />
                </Route>
            </Routes>
        </BrowserRouter>
    );
}

// 主App组件，提供Context
export default function App() {
    return (
        <AppStateProvider>
            <AppContent />
        </AppStateProvider>
    );
}