import React, {useEffect} from "react";
import {Navigate, Outlet} from "react-router-dom";

export default function PrivateRoute({ setLoginState }) {
    const isLoggedIn = sessionStorage.getItem("access_token") != null;
    useEffect(() => {
        if (isLoggedIn) {
            setLoginState(true);
            console.log("登录成功");
            // const storedRadarList = JSON.parse(sessionStorage.getItem("radar_list") || "[]");
            // console.log("加载储存的列表为：",storedRadarList);
            // // const options = storedRadarList.map((radar) => ({
            // //     value: radar.value,    // 这里假设服务器返回的数据包含 id
            // //     label: radar.label,  // 这里假设服务器返回的数据包含 name
            // // }));
            // setRadarList(storedRadarList);
        }
    }, []);
    // useEffect(() => {
    //     console.log("测试，修改当前的列表为：",radarList);
    // }, [radarList]);
    return isLoggedIn ? <Outlet />  : <Navigate to="/test_login" />;
}
