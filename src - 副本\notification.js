import { notification } from 'antd';

/**
 * 显示通知
 * @param {Object} options - 通知选项
 * @param {string} options.type - 通知类型，可以是'success'、'error'、'info'、'warning'
 * @param {string} options.mess - 通知消息内容
 * @param {string} [options.title] - 通知标题，可选
 * @param {number} [options.duration] - 通知显示时间，单位秒，可选，默认为4.5秒
 */
export const openNotification = ({ type, mess, title, duration = 4.5 }) => {
  notification[type]({
    message: title || (type === 'success' ? '成功' : type === 'error' ? '错误' : type === 'warning' ? '警告' : '提示'),
    description: mess,
    duration: duration,
  });
};
