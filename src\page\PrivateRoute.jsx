import React, { useEffect, useRef, useState } from "react";
import { Navigate, Outlet } from "react-router-dom";
import { HomeHead } from "./HomeHead.jsx";
import { Layout } from "antd";
import { NotLoggedIn } from "../notification.jsx";

const { Footer, Content } = Layout;
export default function PrivateRoute({
    radarID,
    setRadarID,
    radarList,
    setRadarList,
    radarState,
    setRadarState,
    loginState,
    setLoginState,
    setSceneCoordinates,
    sceneName
}) {
    const componentRef = useRef(null); // 创建 ref 引用
    const [loginOpenFlag, setLoginOpenFlag] = useState(false);//登录提示弹窗
    const isLoggedIn = sessionStorage.getItem("access_token") != null;
    useEffect(() => {
        if (isLoggedIn) {
            setLoginState(true);
            // console.log("登录成功");
            // console.log("PrivateRoute记录的登陆状态:", loginState);
            // console.log("PrivateRoute记录的场景名称:", sceneName);
            // console.log("PrivateRoute记录的雷达列表:", radarList);
            const storedRadarList = JSON.parse(sessionStorage.getItem("radar_list") || "[]");
            // console.log("PrivateRoute记录的会话雷达列表为：",storedRadarList);
            // // const options = storedRadarList.map((radar) => ({
            // //     value: radar.value,    // 这里假设服务器返回的数据包含 id
            // //     label: radar.label,  // 这里假设服务器返回的数据包含 name
            // // }));
            // setRadarList(storedRadarList);
        }
    }, []);
    // useEffect(() => {
    //     // console.log("测试，修改当前的列表为：",radarList);
    // }, [radarList]);

    // 页面操作管理
    useEffect(() => {
        // 只影响存在的页面
        const component = componentRef.current;
        if (!component) return;

        // 未登录时禁止页面操作
        component.style.pointerEvents = loginState ? 'auto' : 'none';
        console.log(loginState ? '恢复页面交互' : '禁止页面交互');

        // 未登录时弹窗显示
        const handleClick = (e) => {
            if (component.contain(e.target)) {
                setLoginOpenFlag(true);
            }
        }
        if (!loginState) {
            document.addEventListener('click', handleClick);
        }

        // 手动清理，防止内存泄露
        return () => document.removeEventListener('click', handleClick);
    }, [loginState]);
    return isLoggedIn ? (
        <Layout>
            <HomeHead
                radarID={radarID}
                setRadarID={setRadarID}
                radarList={radarList}
                setRadarList={setRadarList}
                radarState={radarState}
                setRadarState={setRadarState}
                loginState={loginState}
                setLoginState={setLoginState}
                setSceneCoordinates={setSceneCoordinates}
                sceneName={sceneName}
                activeNumber="2"
            />
            <Content
                ref={componentRef}
                style={{
                    padding: '24px',
                    minHeight: '280px',
                    backgroundColor: '#fff'
                }}
            >
                <Outlet />
                <NotLoggedIn openFlag={loginOpenFlag} setOpenFlag={setLoginOpenFlag} />
            </Content>
            <Footer style={{ fontSize: 20, textAlign: 'center' }}>
                边坡形变监测系统
            </Footer>
        </Layout>
    ) : <Navigate to="/test_login" />;
}
