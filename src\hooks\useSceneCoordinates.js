import { useState, useEffect } from 'react';
import { OpenNotification } from '../notification';

/**
 * 安全的场景坐标获取Hook
 * 基于当前用户权限和场景ID从后端获取坐标信息
 * 避免在前端存储敏感的坐标数据
 */
export const useSceneCoordinates = (sceneID) => {
    const [coordinates, setCoordinates] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    useEffect(() => {
        if (!sceneID) {
            setCoordinates(null);
            setError(null);
            return;
        }

        const fetchCoordinates = async () => {
            setLoading(true);
            setError(null);
            
            try {
                const response = await fetch("http://127.0.0.1:5000/data_analysis/get_scene_coordinates", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "Authorization": "Bearer " + sessionStorage.getItem("access_token")
                    },
                    body: JSON.stringify({
                        scene_ID: sceneID // 只传递场景ID，后端验证用户权限
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const { status, message, data } = await response.json();
                
                if (status === 'success' && data) {
                    // 后端返回的坐标数据，已经过权限验证
                    const validatedCoordinates = {
                        lon: parseFloat(data.lon) || 0,
                        lat: parseFloat(data.lat) || 0,
                        height: parseFloat(data.height) || 0
                    };
                    setCoordinates(validatedCoordinates);
                } else {
                    throw new Error(message || '获取场景坐标失败');
                }

                OpenNotification({
                    type: status,
                    mess: message
                });

            } catch (error) {
                console.error("获取场景坐标失败:", error);
                setError(error.message);
                setCoordinates(null);
                
                OpenNotification({
                    type: "error",
                    mess: "获取场景坐标失败: " + error.message
                });
            } finally {
                setLoading(false);
            }
        };

        fetchCoordinates();
    }, [sceneID]); // 只依赖sceneID，当场景ID变化时重新获取

    return {
        coordinates,
        loading,
        error,
        // 提供刷新功能，但仍然基于当前sceneID
        refresh: () => {
            if (sceneID) {
                fetchCoordinates();
            }
        }
    };
};
