import React, { createContext, useContext, useState, useEffect } from 'react';

// 创建Context
const AppStateContext = createContext();

// 自定义Hook用于使用Context
export const useAppState = () => {
    const context = useContext(AppStateContext);
    if (!context) {
        throw new Error('useAppState must be used within AppStateProvider');
    }
    return context;
};

// Context Provider组件
export const AppStateProvider = ({ children }) => {
    // 基础状态
    const [loginState, setLoginState] = useState(() => {
        return !!sessionStorage.getItem("access_token");
    });

    const [sceneName, setSceneName] = useState(() => {
        return sessionStorage.getItem("sceneName");
    });

    const [radarList, setRadarList] = useState(() => {
        return JSON.parse(sessionStorage.getItem("radar_list") || "[]");
    });

    const [radarID, setRadarID] = useState(() => {
        return sessionStorage.getItem("radar_id");
    });

    // 不需要持久化的状态
    const [missionID, setMissionID] = useState(null);
    const [showTimeSelected, setShowTimeSelected] = useState("0");
    const [radarInformation, setRadarInformation] = useState([]);
    const [sceneCoordinates, setSceneCoordinates] = useState([]);

    const [radarState, setRadarState] = useState(() => {
        const storedState = sessionStorage.getItem("radarState");
        if (storedState) {
            try {
                return JSON.parse(storedState);
            } catch (e) {
                console.error("解析 radarState 失败:", e);
            }
        }
        return {
            color: "#fa1c30",
            text: "离线",
        };
    });

    // 登出时清理状态
    useEffect(() => {
        if (loginState) {
            return;
        }
        setSceneName(null);
        sessionStorage.removeItem('sceneName');
        setRadarList([]);
        setRadarID(null);
        setMissionID(null);
        setShowTimeSelected("0");
        setRadarInformation([]);
        setSceneCoordinates([]); // 清理场景坐标
        setRadarState({
            text: "未在线",
            color: "#fa1c30",
        });
    }, [loginState]);

    // 监听 radarState 变化，更新 sessionStorage
    useEffect(() => {
        if (radarState) {
            sessionStorage.setItem("radarState", JSON.stringify(radarState));
            console.log("更新 radarState 到 sessionStorage:", radarState);
        }
    }, [radarState]);

    // 提供给子组件的值
    const value = {
        // 状态
        loginState,
        sceneName,
        radarList,
        radarID,
        missionID,
        showTimeSelected,
        radarInformation,
        sceneCoordinates,
        radarState,

        // 状态更新函数
        setLoginState,
        setSceneName,
        setRadarList,
        setRadarID,
        setMissionID,
        setShowTimeSelected,
        setRadarInformation,
        setSceneCoordinates,
        setRadarState,
    };

    return (
        <AppStateContext.Provider value={value}>
            {children}
        </AppStateContext.Provider>
    );
};
