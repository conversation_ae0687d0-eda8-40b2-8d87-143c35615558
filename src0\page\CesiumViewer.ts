import * as Cesium from "cesium";
import {SpaceProps} from "antd";
import {OpenNotification} from "../notification"

type AngleUnit = 'degrees' | 'radians';

/**
 * @interface Destination
 * @description 地理位置信息接口
 * @property {number} lon - 经度
 * @property {number} lat - 纬度
 * @property {number} height - 高度
 * @property {AngleUnit} unit - 角度值的单位。
 */
interface Destination {
    lon: number;
    lat: number;
    height: number;
    unit: AngleUnit;
}


/**
 * @interface Orientation
 * @description 定义三维空间中的朝向或姿态，并指定单位。
 * @property {number} heading - 航向角。
 * @property {number} pitch - 俯仰角。
 * @property {number} roll - 翻滚角。
 * @property {AngleUnit} unit - 角度值的单位。
 */
interface Orientation {
    heading: number;
    pitch: number;
    roll: number;
    unit: AngleUnit;
}

/**
 * @interface RectangleCoordinates
 * @description 定义地理矩形区域的四个角点坐标。
 * @property {number} west - 西经
 * @property {number} south - 南纬
 * @property {number} east - 东经
 * @property {number} north - 北纬
 */
interface RectangleCoordinates {
    west: number;
    south: number;
    east: number;
    north: number;
}

/**
 * @interface MonitorArea
 * @description 定义检测区域的数据类型
 * @property {string} key - 标识
 * @property {string} label - 区域名称
 * @property {React.ReactElement<SpaceProps>} children - 检测区域展示时的渲染内容
 * @property {React.ReactNode} extra - 改变 children 的渲染效果
 * @property {string} description - 描述
 * @property {number} westLon - 西边经度
 * @property {number} eastLon - 东边经度
 * @property {number} southLat - 南边纬度
 * @property {number} northLat - 北边纬度
 */
interface MonitorArea {
    key: string;
    label: string;
    children: React.ReactElement<SpaceProps>;
    extra: React.ReactNode;
    description: string;
    westLon: number;
    eastLon: number;
    southLat: number;
    northLat: number;
}

/**
 * @class CesiumViewer
 * @description Cesium三维地球视图封装类，提供场景初始化、相机控制、实体添加、事件处理等功能。
 */
export class CesiumViewer {
    /** 用来显示经纬度信息的那个 div 元素 */
    geoInfoBox: HTMLElement;
    /** 一个状态开关：是不是正在“画矩形区域”？ (true/false) */
    areaPickFlag: boolean = false;
    /** 一个状态开关：是不是正在“量距离”？ (true/false) */
    distFlag: boolean = false;
    /** 画图或量距的起始点坐标 {lon, lat, height} */
    startPoint: Destination | null = null;
    /** 画图或量距的终点（或当前鼠标点）坐标 {lon, lat, height} */
    endPoint: Destination | null = null;
    /** 鼠标当前位置的坐标 */
    currPoint: Destination | null = null;
    /** 当前正在绘制的那个半透明红色矩形实体 */
    currentAreaEntity: Cesium.Entity | null = null;
    /** 存储所有黄色的“监测区域”矩形实体 */
    monitorAreaEntities: Cesium.Entity[] = [];
    /** 一个状态开关：是否要显示所有黄色的“监测区域”？ */
    monitorAreaShowFlag: boolean = true;
    /** 雷达图标实体 */
    radarHost: Cesium.Entity | null = null;
    /** 一个状态开关：是不是正在“拾取单个坐标点”？ */
    pickOnePointFlag: boolean = false;
    /** Cesium.Viewer绑定的HTML容器ID */
    CesiumViewerID: string = "";
    /** 图片贴图图层 */
    PicLayer: Cesium.ImageryLayer | null = null;
    /** 地形数据提供者 */
    terrainProvider: Cesium.TerrainProvider | null = null;
    /** 一个状态开关：登录页的地球是否要自动旋转？ */
    rotateEnabled: boolean = false;
    /** 最核心的`Cesium.Viewer`对象！ */
    viewer: Cesium.Viewer | null = null;
    /** 优化：添加事件监听器引用，便于清理, 只在build login和destroy里面使用。目的是规避内存泄漏, 地球越转越快 */
    postUpdateHandler: (() => void) | null = null;

    /**
     * @constructor
     * @param {string} containerId - Cesium.Viewer将要绑定的HTML元素的ID。
     */
    constructor(containerId: string) {
        this.CesiumViewerID = containerId;
    }

    /**
     * @method build_login
     * @description 初始化并构建用于登录页的Cesium场景，特点是地球会自动旋转。
     * @returns {Promise<void>}
     */
    async build_login(): Promise<void> {
        this.rotateEnabled = true;
        if (this.viewer && !this.viewer.isDestroyed()) {
            console.log('登录界面处的viewer已经初始化了');
            return
        }
        // https://cesium.com/learn/ion-sdk/ref-doc/Viewer.html
        // console.log("create earth");
        this.viewer = new Cesium.Viewer(this.CesiumViewerID, {
            infoBox: false,   // 关闭信息框，解决报错
            // geocoder: false,  // 关闭右上角搜索输入
            homeButton: false,  // 关闭右上角home按钮，功能是回到默认视场
            sceneModePicker: false,   // 关闭右上角2D/3D选择按钮
            baseLayerPicker: false,   // 关闭图层选择
            navigationHelpButton: false,  // 关闭右上角帮助按钮
            animation: false,   // 关闭动画，左下角的圆形控件
            timeline: false,  // 关闭时间线
            fullscreenButton: false,   // 去掉右下角的全屏按钮
            geocoder: false,           //右上角的查询按钮，可以根据输入的地名地址，定位到查询结果
            requestRenderMode: true,   // 仅在需要时渲染场景
            maximumRenderTimeChange: Infinity, // 无论场景如何变化都渲染
        });
        // 去除cesium logo水印
        // 老版本方法
        (this.viewer.cesiumWidget.creditContainer as HTMLElement).style.display = "none";
        // https://stackoverflow.com/questions/76015471/how-can-i-prevent-the-cesium-ion-logo-from-showing-up
        // this.viewer.cesiumWidget.creditContainer.parentNode?.removeChild(this.viewer.cesiumWidget.creditContainer);

        this.setView({lon: 40, lat: 17, height: 7000000, unit: "degrees"}, {
            heading: 270,
            pitch: -70,
            roll: 90,
            unit: "degrees"
        })

        // 在添加新的动画之前，先检查一下是不是已经有一个旧的动画在运行了
        if (this.postUpdateHandler) {
            this.viewer.scene.postUpdate.removeEventListener(this.postUpdateHandler);
        }

        // postUpdate 的回调函数是异步执行的，在它执行时，TypeScript 无法确定 this.viewer 是否已经被其他代码（例如 destroy 方法）设置为 null ，因此会提示“对象可能为 'null'”的错误，以防止潜在的运行时错误。
        this.postUpdateHandler = this.viewer.scene.postUpdate.addEventListener(() => {
            if (this.rotateEnabled && this.viewer) {
                this.viewer.scene.camera.rotate(Cesium.Cartesian3.UNIT_Z, -0.0005);
            }
        });
    }

    /**
     * @method build
     * @description 创建数据监测页面地球
     * @param {Destination} sceneCoordinates - 场景坐标
     */
    build = async (sceneCoordinates: Destination) => {
        if (this.viewer && !this.viewer.isDestroyed()) {
            console.log("场景viewer已被创建");
            return;
        }
        console.log("开始创建场景viewer");
        this.viewer = new Cesium.Viewer(this.CesiumViewerID, {
            infoBox: false,   // 关闭信息框，解决报错
            // geocoder: false,  // 关闭右上角搜索输入
            homeButton: false,  // 关闭右上角home按钮，功能是回到默认视场
            sceneModePicker: false,   // 关闭右上角2D/3D选择按钮
            baseLayerPicker: false,   // 关闭图层选择
            navigationHelpButton: false,  // 关闭右上角帮助按钮
            animation: false,   // 关闭动画，左下角的圆形控件
            timeline: false,  // 关闭时间线
            fullscreenButton: false,   // 去掉右下角的全屏按钮
            geocoder: false,           //右上角的查询按钮，可以根据输入的地名地址，定位到查询结果
            requestRenderMode: true,   // 仅在需要时渲染场景
            maximumRenderTimeChange: Infinity, // 无论场景如何变化都渲染
        });
        (this.viewer.cesiumWidget.creditContainer as HTMLElement).style.display = "none";
        this.setView(
            sceneCoordinates,
            {heading: 0, pitch: -90, roll: 0, unit: "degrees"}
        );
        try {
            await (this.viewer.scene.completeMorph() as unknown) as Promise<void>;
            console.log("地形和影像加载完成");
            this.flyTo(sceneCoordinates);
        } catch (error) {
            OpenNotification({
                type: "error",
                mess: "地球资源加载失败" + error
            });
        }
        this.setGeoInfoBox();
        this.addMouseEvent();
        this.geoInfoBox.innerText = '请移动鼠标以查看实时坐标';
        await this.addCustomTerrainAsync(this.viewer);
    }

    /**
     * @method addCustomTerrainAsync
     * @description 传入DEM
     * @param {Cesium.Viewer} viewer
     */
    addCustomTerrainAsync = async (viewer: Cesium.Viewer) => {
        console.log('加载地形数据');
        try {
            // 将自定义地形提供者赋值给 viewer
            viewer.terrainProvider = await Cesium.CesiumTerrainProvider.fromUrl(
                "http://localhost:5000/download/DEM/test_5_8", {
                    requestWaterMask: true,
                    requestVertexNormals: true,
                }
            );
        } catch (error) {
            console.error(`Failed to add custom terrain: ${error}`);
        }
    }

    /**
     * @method addWorldTerrainAsync
     * @description 添加官方地形数据
     * @param {Cesium.Viewer} viewer
     */
    addWorldTerrainAsync = async (viewer) => {
        try {
            // 将加载的地形数据提供给 viewer
            viewer.terrainProvider = await Cesium.createWorldTerrainAsync({
                requestWaterMask: true,
                requestVertexNormals: true,
            });
        } catch (error) {
            console.error(`Failed to add world terrain: ${error}`);
        }
    };

    /**
     * @method flyTo
     * @description 控制相机飞行到指定位置。
     * @param {Destination} [destination={ lon: 113.950, lat: 22.805, height: 5000000, unit: "degrees" }] - 目标点的经纬高坐标, 单位可以是'degrees'或'radians'。
     * @param {number} [flyTime=5] - 飞行动画的持续时间（秒）。
     */
    flyTo(destination: Destination = {
        lon: 113.950,
        lat: 22.805,
        height: 5000000,
        unit: "degrees"
    }, flyTime: number = 5): void {
        if (!this.viewer) {
            console.error("flyTo 请先调用 build_login or build 构建地球！");
            return;
        }
        let destinationCartesian: Cesium.Cartesian3;
        if (destination.unit == "degrees") {
            destinationCartesian = Cesium.Cartesian3.fromDegrees(destination.lon, destination.lat, destination.height);
        } else {
            destinationCartesian = Cesium.Cartesian3.fromRadians(destination.lon, destination.lat, destination.height)
        }
        this.viewer.camera.flyTo({
            destination: destinationCartesian,
            duration: flyTime
        });
    }


    /**
     * @method setView
     * @description 直接设置相机的视角和位置。该方法智能处理角度和弧度单位。
     * @param {Destination} [destination = { lon: 40, lat: 17, height: 7000000, unit: "degrees" }] - 相机位置，单位可以是'degrees'或'radians'。
     * @param {Orientation} [orientation = { heading: 0, pitch: -30, roll: 0, unit: "degrees" }] - 相机视角，单位可以是'degrees'或'radians'。
     */
    setView(destination: Destination = {lon: 40, lat: 17, height: 7000000, unit: "degrees"},
            orientation: Orientation = {heading: 0, pitch: -30, roll: 0, unit: "degrees"}): void {

        if (!this.viewer) {
            console.error("setView 请先调用 build_login or build 构建地球！");
            return;
        }

        let destinationCartesian: Cesium.Cartesian3;
        let orientationHPR: Cesium.HeadingPitchRoll;

        // 1. 根据单位，用最直接的方式处理相机位置
        if (destination.unit === 'degrees') {
            destinationCartesian = Cesium.Cartesian3.fromDegrees(destination.lon, destination.lat, destination.height);
        } else { // 单位是 'radians'
            destinationCartesian = Cesium.Cartesian3.fromRadians(destination.lon, destination.lat, destination.height);
        }

        // 2. 根据单位，用最直接的方式处理相机朝向
        if (orientation.unit === 'degrees') {
            orientationHPR = Cesium.HeadingPitchRoll.fromDegrees(orientation.heading, orientation.pitch, orientation.roll);
        } else { // 单位是 'radians'
            // new Cesium.HeadingPitchRoll() 构造函数本身就接收弧度作为参数
            orientationHPR = new Cesium.HeadingPitchRoll(orientation.heading, orientation.pitch, orientation.roll);
        }

        // 3. 一次性设置视图
        this.viewer.camera.setView({
            destination: destinationCartesian,
            orientation: orientationHPR /* 恰好也拥有 heading, pitch, roll 这三个数字属性 */
        });
    }

    /**
     * @method addPic
     * @description 在地球表面的一个精确指定的矩形区域内，贴上一张半透明的图片。。
     * @param {string} ImgUrl - 图片的URL地址。
     * @param {RectangleCoordinates} [rectangleCoords = {
     *         west: 113.0557630859,
     *         south: 23.3492091920,
     *         east: 113.0666752406,
     *         north: 23.3630087798
     *     }] - 区域西南东北经纬度
     * @param {number} [alpha=0.5] - 图片透明度
     */
    addPic(ImgUrl: string,
           rectangleCoords: RectangleCoordinates = {
               west: 113.0557630859,
               south: 23.3492091920,
               east: 113.0666752406,
               north: 23.3630087798
           },
           alpha: number = 0.5
    ): void {
        if (!this.viewer) {
            console.error("addPic 请先调用 build构建地球！");
            return;
        }

        // 从传入的参数动态创建 Rectangle 对象
        const targetRectangle = Cesium.Rectangle.fromDegrees(
            rectangleCoords.west,
            rectangleCoords.south,
            rectangleCoords.east,
            rectangleCoords.north
        );

        // 在这个图层集合中添加一个新的影像图层。这个新图层会叠加在已有图层的上方。
        this.PicLayer = this.viewer.imageryLayers.addImageryProvider(
            // 专门用来处理单张图片作为图源的情况
            new Cesium.SingleTileImageryProvider({
                url: ImgUrl,
                rectangle: targetRectangle,
                tileHeight: 256,
                tileWidth: 256
            })
        );
        this.PicLayer.alpha = alpha;
        console.log("雷达图片贴图成功");
    }

    /**
     * @method delePic
     * @description 删除已添加的图片图层。
     */
    delePic(): void {
        if (!this.viewer) {
            console.error("delePic 请先调用 build构建地球！");
            return;
        }
        if (this.PicLayer) {
            this.viewer.imageryLayers.remove(this.PicLayer);
            this.PicLayer = null;
        }
    }

    /**
     * @method opacityPic
     * @description 调整图片图层的透明度。
     * @param {number} opacity - 透明度值，范围从0.0（完全透明）到1.0（完全不透明）。
     */
    opacityPic(opacity: number): void {
        if (!this.viewer) {
            console.error("opacityPic 请先调用 build构建地球！");
            return;
        }
        if (this.PicLayer) {
            this.PicLayer.alpha = opacity;
        }
    }

    /**
     * @method addRadarHost
     * @description 在指定位置添加一个雷达图标实体。
     * @param {number[]} position - 雷达图标的位置坐标 `[lon, lat, height]`。
     */
    addRadarHost(position: number[]): void {
        if (!this.viewer) {
            console.error("addRadarHost 请先调用 build构建地球！");
            return;
        }
        if (this.radarHost) {
            this.viewer.entities.remove(this.radarHost);
            this.radarHost = null;
        }
        if (position.length !== 3) {
            return;
        }
        this.radarHost = this.viewer.entities.add({
            position: Cesium.Cartesian3.fromDegrees(position[0], position[1], position[3]),
            billboard: {
                image: '/pic/radarhost.png',
                color: Cesium.Color.WHITE.withAlpha(0.8),

                // 像素为单位
                height: 50,
                width: 50,

                // 顺时针转
                rotation: 0,

                // true: width 和 height 的单位是 米。图标在三维空间中拥有真实大小，当你拉近相机会变大，拉远相机会变小。这对于模拟真实世界物体的尺寸非常有用。
                sizeInMeters: false, // 不以米为单位

                // 在您的设置中 (CENTER, LEFT)，意味着图像 左侧边的中点 将被放置在实体的 position 上
                verticalOrigin: Cesium.VerticalOrigin.CENTER,
                horizontalOrigin: Cesium.HorizontalOrigin.LEFT,

                // new Cesium.Cartesian2(x, y) 中，x 正值向右偏移，y 正值向下偏移
                pixelOffset: new Cesium.Cartesian2(0, 0),
                scale: 1,

                // 可视化范围
                distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 50000),
                show: true
            }
        })
    }


    /**
     * @method setGeoInfoBox
     * @description 创建并配置用于显示实时地理坐标的UI面板。
     */
    setGeoInfoBox = (): void => {
        if (!this.viewer) {
            console.error("setGeoInfoBox 请先调用 build构建地球！");
            return;
        }
        const geoInfoBox = document.createElement('div');
        (this.viewer.container as HTMLElement).style.position = 'relative';
        this.viewer.container.appendChild(geoInfoBox);
        geoInfoBox.className = 'backdrop';
        geoInfoBox.style.position = 'absolute';
        geoInfoBox.style.bottom = '5px';
        geoInfoBox.style.right = '0';
        geoInfoBox.style['pointerEvents'] = 'none';
        geoInfoBox.style['whiteSpace'] = 'pre';
        geoInfoBox.style.padding = '2px';
        geoInfoBox.style.backgroundColor = 'black';
        geoInfoBox.style.width = '70%';
        geoInfoBox.style.paddingRight = '10%'
        geoInfoBox.style.fontSize = '8pt';
        geoInfoBox.style.color = 'white';
        geoInfoBox.style.textAlign = 'right';
        this.geoInfoBox = geoInfoBox;
    }

    /**
     * @method addMouseEvent
     * @description 添加鼠标移动事件监听，实时更新地理信息框中的经纬高信息。
     */
    addMouseEvent(): void {
        if (!this.viewer) {
            console.error("addMouseEvent 请先调用 build构建地球！");
            return;
        }
        const handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
        handler.setInputAction((movement) => {
            const {lon, lat, height} = this.getPosInfo(movement.endPosition);
            this.geoInfoBox.innerText = `经度: ${lon.toFixed(5)} 纬度: ${lat.toFixed(5)} 高度: ${height.toFixed(3)}`
        }, Cesium.ScreenSpaceEventType.MOUSE_MOVE)
    }

    /**
     * @method calcDist
     * @description 启动距离测量模式。用户在地图上点击两点后，计算并返回两点间的直线距离。
     * @param {function(string): void} callback - 测量完成后调用的回调函数，接收一个包含距离信息的字符串作为参数。
     */
    calcDist = (callback: (distance: string) => void): void => {
        if (!this.viewer) {
            console.error("calcDist 请先调用 build构建地球！");
            return;
        }
        const handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
        let lineEntity: Cesium.Entity | null = null;
        handler.setInputAction((event) => {
            if (!this.viewer) {
                console.error("calcDist LEFT_CLICK 请先调用 build构建地球！");
                return;
            }
            if (this.distFlag) {
                if (this.startPoint === null) {
                    this.startPoint = this.getPosInfo(event.position);
                    console.log("距离计算开始点", this.startPoint);
                    if (this.startPoint === null) return;
                    // this.endPoint = this.getPosInfo(event.position);
                    this.endPoint = this.startPoint;
                    if (this.endPoint === null) return;
                    lineEntity = this.viewer.entities.add({
                        polyline: {
                            // 允许属性值是动态计算的
                            positions: new Cesium.CallbackProperty(() => {
                                // 在回调函数内部也需要确保非 null
                                if (this.startPoint && this.endPoint) { // 检查
                                    return [
                                        Cesium.Cartesian3.fromDegrees(this.startPoint.lon, this.startPoint.lat, this.startPoint.height),
                                        Cesium.Cartesian3.fromDegrees(this.endPoint.lon, this.endPoint.lat, this.endPoint.height),
                                    ]; // 第一个参数是一个函数 () => { return [Cartesian3, Cartesian3]; }，这个函数会定期被 Cesium 调用，以获取线的最新端点坐标。
                                } else {
                                    return []; // 或返回两个相同点，或处理错误
                                }
                            }, false), // 第二个参数 false 表示这个属性不是时间动态的（即不依赖于 viewer.clock）。
                            width: 4,
                            material: Cesium.Color.RED.withAlpha(0.8)
                        }
                    })
                } else {
                    this.distFlag = false;
                    console.log("结束点", this.endPoint);
                    // 计算距离时也需要确保 startPoint/endPoint 不为 null
                    if (this.startPoint && this.endPoint) {
                        const point1 = Cesium.Cartesian3.fromDegrees(this.startPoint.lon, this.startPoint.lat, this.startPoint.height);
                        const point2 = Cesium.Cartesian3.fromDegrees(this.endPoint.lon, this.endPoint.lat, this.endPoint.height);
                        const distance = '两点距离为' + Cesium.Cartesian3.distance(point1, point2).toString() + 'm';
                        callback(distance);
                    } else {
                        return;
                    }

                    setTimeout(() => {
                        if (lineEntity && this.viewer) {
                            this.viewer.entities.remove(lineEntity);
                        }
                        this.startPoint = null;
                        this.endPoint = null;
                    }, 3000);
                }
            }
            return;
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
        handler.setInputAction((movement) => {
            // 是否选择初始点
            if (!this.distFlag) return;
            this.endPoint = this.getPosInfo(movement.endPosition);
        }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    }

    /**
     * @method addPickArea
     * @description 启动区域拾取模式。用户在地图上通过拖拽绘制一个矩形区域。
     * @param {function(any): void} callback - 区域选择完成后调用的回调函数。
     */
    addPickArea = (callback): void => {
        if (!this.viewer) {
            console.error("addPickArea 请先调用 build构建地球！");
            return;
        }
        const handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
        handler.setInputAction((event) => {
            if (this.areaPickFlag) {
                if (this.startPoint === null) {
                    this.startPoint = this.getPosInfo(event.position);
                    console.log("区域选取开始点", this.startPoint);
                    if (this.startPoint === null) return;
                    this.endPoint = this.startPoint;
                    if (this.endPoint === null) return;
                    this.currentAreaEntity = this.viewer.entities.add({
                        rectangle: {
                            coordinates: new Cesium.CallbackProperty(() => {
                                return Cesium.Rectangle.fromDegrees(
                                    Math.min(this.startPoint.lon, this.endPoint.lon),
                                    Math.min(this.startPoint.lat, this.endPoint.lat),
                                    Math.max(this.startPoint.lon, this.endPoint.lon),
                                    Math.max(this.startPoint.lat, this.endPoint.lat)
                                );
                            }, false),
                            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                            material: Cesium.Color.RED.withAlpha(0.3)
                        }
                    })
                } else {
                    this.areaPickFlag = false;
                    console.log("结束点", this.endPoint);
                    callback();
                }
            } else {
                return;
            }
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
        handler.setInputAction((movement) => {
            // 是否选择初始点
            if (!this.areaPickFlag) return;
            this.endPoint = this.getPosInfo(movement.endPosition);
        }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    }

    /**
     * @method cancelArea
     * @description 清除选定区域
     */
    cancelArea = () => {
        this.viewer.entities.remove(this.currentAreaEntity);
        this.startPoint = null;
        this.endPoint = null;
    }

    /**
     * @method addMonitorArea
     * @description 显示监测矩形区域
     * @param {MonitorArea[]} [monitorArea] - 检测区域列表
     * @param {MonitorArea[]} [activeMonitorArea] - 待添加的区域列表
     */
    addMonitorArea = (monitorArea: MonitorArea[], activeMonitorArea: MonitorArea[]) => {
        console.log("监测区域传入地球：", monitorArea);
        // 删除所有矩形
        if (this.monitorAreaEntities.length > 0) {
            this.monitorAreaEntities.forEach(entity => this.viewer.entities.remove(entity));
            this.monitorAreaEntities = [];
        }
        if (!monitorArea || monitorArea.length === 0 || !this.monitorAreaShowFlag) {
            return;
        }
        monitorArea.forEach((area: MonitorArea) => {
            let alpha = 0.2;
            // 选中高亮
            if (activeMonitorArea.includes(area)) {
                alpha = 0.5;
            }
            const rectangle = this.viewer.entities.add({
                name: area.label,
                rectangle: {
                    coordinates: Cesium.Rectangle.fromDegrees(
                        area.westLon,
                        area.southLat,
                        area.eastLon,
                        area.northLat
                    ),
                    material: Cesium.Color.YELLOW.withAlpha(alpha),
                    heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
                },
                label: {
                    text: "监测区域"
                }
            });
            this.monitorAreaEntities.push(rectangle);
        })
    }

    /**
     * @method getPosInfo
     * @description 根据屏幕坐标获取其在地球上的地理坐标（经度、纬度、高度）。
     * @param {Cesium.Cartesian2} position - 屏幕上的二维坐标。
     * @returns {Destination} - 包含经、纬、高的地理位置对象。
     */
    getPosInfo(position: Cesium.Cartesian2): Destination {
        if (!this.viewer) {
            console.error("getPosInfo 请先调用 build构建地球！");
            return null; // 返回 null
        }
        const ray = this.viewer.camera.getPickRay(position);
        if (!ray) return null; // 返回 null
        const cartesian = this.viewer.scene.globe.pick(ray, this.viewer.scene);
        if (!cartesian) return null; // 返回 null
        const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
        const lon = Cesium.Math.toDegrees(cartographic.longitude);
        const lat = Cesium.Math.toDegrees(cartographic.latitude);
        const height = cartographic.height;
        return {lon, lat, height, unit: "degrees"}
    }

    /**
     * @method getOnePoint
     * @description 左键单击获取单个坐标
     * @param {(number[]) => {}} callback - 接受经、纬、高参数的回调函数
     */
    getOnePoint = (callback) => {
        const handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
        handler.setInputAction((clickEvent) => {
            if (this.pickOnePointFlag) {
                const position = this.getPosInfo(clickEvent.position);
                callback([position.lon, position.lat, position.height]);
                OpenNotification({
                    type: "success",
                    mess: "选择坐标成功"
                });
                this.pickOnePointFlag = true;
            }
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK)
    }

    /**
     * @method destroy
     * @description 销毁Cesium Viewer实例并清理所有相关资源，以防止内存泄漏。
     */
    destroy(): void {
        if (!this.viewer) {
            console.error("destroy 请先调用 build_login or build 构建地球！");
            return;
        }
        this.areaPickFlag = false;
        this.distFlag = false;
        this.startPoint = null;
        this.endPoint = null;
        this.currPoint = null;
        this.currentAreaEntity = null;
        this.monitorAreaShowFlag = true;
        this.pickOnePointFlag = false;
        this.rotateEnabled = false;

        // 清理所有实体和图层
        this.monitorAreaEntities = [];
        this.radarHost = null;
        if (this.PicLayer) {
            this.viewer.imageryLayers.remove(this.PicLayer);
            this.PicLayer = null;
        }

        // 移除事件监听器
        if (this.postUpdateHandler) {
            this.viewer.scene.postUpdate.removeEventListener(this.postUpdateHandler);
            this.postUpdateHandler = null;
        }

        // 销毁viewer实例
        if (this.viewer && !this.viewer.isDestroyed()) {
            this.viewer.destroy();
        }
        this.viewer = null;
        this.terrainProvider = null;
        console.log("Cesium Viewer已销毁");
    }

}