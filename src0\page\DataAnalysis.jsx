import {
    Badge,
    Card,
    Col,
    Row,
    Select,
    Radio,
    Form,
    Space,
    DatePicker,
    Button,
    Flex,
    Collapse,
    Modal,
    Descriptions, Tooltip, Slider
} from "antd";
import {use, useEffect, useRef, useState} from "react";
import {OpenNotification} from "../notification.jsx";
import {
    BoxPlotOutlined, DashboardOutlined,
    DeleteOutlined, HomeOutlined,
    Pie<PERSON>hartOutlined,
    RadiusBottomrightOutlined, SmileTwoTone, VideoCameraOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs'
import {CesiumViewer} from "./CesiumViewer.js";

const viewer = new CesiumViewer("cesiumContainer");
export function DataAnalysis({
                                 setRadarID,
                                 radarID,
                                 radarList,
                                 setRadarList,
                                 radarState,
                                 setRadarState,
                                 loginState,
                                 setLoginState,
                                 missionID,
                                 setMissionID,
                                 formCardShowData,
                                 setShowTimeSelected,
                                 showTimeSelected,
                                 sceneCoordinates,
                                 setSceneCoordinates,
                                 sceneName
                             }) {
    const [missionList, setMissionList] = useState([]); // 存储任务列表
    const [showDeformationRange, setShowDeformationRange] = useState([]); //选择的形变区域时间
    // console.log("DataAnalysis页面记录的radarState为", radarState)
    const [activeMonitorArea, setActiveMonitorArea] = useState([])//高亮监测区域
    const [monitorArea, setMonitorArea] = useState([]);
    /**
     * @method getSceneCoordinates
     * @description 获取场景坐标
     */
    const getSceneCoordinates = async () => {
        try {
            const response = await fetch("http://127.0.0.1:5000/data_analysis/get_scene_coordinates", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "Authorization": "Bearer " + sessionStorage.getItem("access_token")
                },
                body: JSON.stringify({
                    scene_ID: sessionStorage.getItem("sceneID")
                })
            });
            const { status, message, data } = await response.json();
            console.log("收到雷达场景坐标数据", data)
            if (response.ok) {
                setSceneCoordinates((data || []).map(parseFloat))
            } else {
                setSceneCoordinates([])
            }
            OpenNotification({
                type: status,
                mess: message
            })
        } catch (error) {
            console.error("获取雷达场景坐标失败:", error);
            setSceneCoordinates([]);
            OpenNotification({
                type: "error",
                mess: "获取雷达场景坐标失败，" + error
            });
        }
    }

    /**
     * @method getMission_list
     * @description 获取任务列表
     * @param {string} [radarID] - 雷达ID
     */
    const getMission_list = async (radarID) => {
        try {
            const response = await fetch("http://127.0.0.1:5000/data_analysis/check_all_mission", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    'Authorization': 'Bearer ' + sessionStorage.getItem("access_token"),
                },
                body: JSON.stringify({ radar_ID: radarID }),  // 发送表单的 ID
            });
            const {status, message, data} = await response.json();
            if (response.ok) {
                setMissionList(
                    (data || []).map((radar) => ({
                        value: radar.mission_ID,
                        label: radar.name
                    }))
                );
            } else {
                setMissionList([]);
            }
            OpenNotification({
                type: status,
                mess: message
            });
        } catch (error) {
            console.log("无法获取雷达" + {radarID} + "的任务列表" + {error});
            OpenNotification({
                type: "error",
                mess: "无法获取雷达" + {radarID} + "的任务列表" + {error}
            });
        }
    }

    // 获取任务列表
    useEffect(() => {
        if (!radarID) return;
        getMission_list(radarID);
    }, [radarID]);

    return (
        <Row>
            <Col span={8}>
                <CardRadarControl radarList={radarList}
                                  radarID={radarID}
                                  setRadarID={setRadarID}
                                  radarState={radarState}/>
                <CardShowData
                    missionList={missionList}
                    missionID={missionID}
                    setMissionID={setMissionID}
                    radarID={radarID}
                    form={formCardShowData}
                    showTimeSelected={showTimeSelected}
                    setShowTimeSelected={setShowTimeSelected}
                    setShowDeformationRange={setShowDeformationRange}
                />
                <CardShowMonitorArea
                    radarID={radarID}
                    missionID={missionID}
                    monitorArea={monitorArea}
                    setMonitorArea={setMonitorArea}
                    setActiveMonitorArea={setActiveMonitorArea}
                />
            </Col>
            <Col span={16}>
                <CardShowEarth
                    radarID={radarID}
                    missionID={missionID}
                    monitorArea={monitorArea}
                    activeMonitorArea={activeMonitorArea}
                    sceneCoordinates={sceneCoordinates}
                    setSceneCoordinates={setSceneCoordinates}
                />
                <CardDeformationLine
                    radarID={radarID}
                    missionID={missionID}
                    activeMonitorArea={activeMonitorArea}
                    showDeformationRange={showDeformationRange}
                    showTimeSelected={showTimeSelected}
                />
            </Col>
        </Row>
    );
}

const CardRadarControl = ({radarList, radarID, setRadarID, radarState}) => {
    console.log("初始 radarState:", radarState);
    // // console.log(radarList.filter(item => item.value === radarID)[0].label)
    const [buttonAble, setButtonAble] = useState(false);
    console.log("初始 buttonAble:", false);


    const radarControl = async (e) => {
        if (!radarID) {
            setButtonAble(false);
            OpenNotification({
                type: "warning",
                mess: "请选择雷达"
            })
            return;
        }
        // console.log("雷达控制数据: ", e.target.value);
        const dataToSend = {
            is_work: e.target.value,
            radar_ID: radarID
        };
        try {
            const response = await fetch("http://127.0.0.1:5000/data_analysis/radar_control", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "Authorization": "Bearer " + sessionStorage.getItem("access_token")
                },
                body: JSON.stringify(dataToSend)
            });
            const {status, message} = await response.json();
            OpenNotification({
                type: status,
                mess: message
            })
            if (response.ok) {
                console.log('成功控制雷达');
            }
        } catch (error) {
            OpenNotification({
                type: "error",
                mess: error
            });
        }
    }
    useEffect(() => {
        console.log("radarState.text 变化:", radarState.text);
        if (radarState.text === '离线') {
            console.log("雷达离线，设置 buttonAble 为 false");
            setButtonAble(false);
        } else {
            console.log("雷达在线，设置 buttonAble 为 true");
            setButtonAble(true);
        }
    }, [radarState.text]);

    useEffect(() => {
        console.log("buttonAble:", buttonAble)
    }, [buttonAble]);

    return (
        <Card title={"雷达控制"} variant={"outlined"}>
            <Row style={{marginBottom: 16}} align={"middle"}>
                <Col span={14}>
                    <Row justify={"space-around"} align={"middle"}>
                        <span>雷达: </span>
                        <Select style={{width: "70%"}}
                                options={radarList}
                                value={radarID}
                                onChange={setRadarID}
                                // disabled
                                // suffixIcon
                        />
                    </Row>
                </Col>
                <Col span={10}>
                    <Row justify={"space-around"} align={"middle"}>
                        <span>状态: </span>
                        <Badge count={0} showZero
                               style={{
                                   backgroundColor: radarState.color,
                                   color: radarState.color,
                                   marginRight: 8
                               }}
                        />
                    </Row>
                </Col>
            </Row>
            <Row align={"middle"} justify={"space-around"}>
                <Col span={23}>
                    <span style={{marginRight: "25px"}}>控制:</span>
                    {/*<div>*/}
                    {/*<p style={{ color: 'red' }}>当前禁用状态: {!buttonAble ? "已禁用" : "未禁用"} (buttonAble: {buttonAble.toString()})</p>*/}
                    <Radio.Group
                        onChange={radarControl}
                        disabled={!buttonAble}
                    >
                        <Radio.Button value={0}>停止工作</Radio.Button>
                        <Radio.Button value={1}>开始工作</Radio.Button>
                    </Radio.Group>
                    {/*</div>*/}
                </Col>
            </Row>
        </Card>
    )
}

const CardShowData = ({radarID, missionList, form, missionID, setMissionID, showTimeSelected, setShowTimeSelected, setShowDeformationRange}) => {
    const [missionTimeRange, setMissionTimeRange] = useState({
        minDate: '2025-03-07 00:00',
        maxDate: '2025-03-07 23:59'
    });

    /**
     * @method updateMissionTimeRange
     * @description 获取对应雷达设备的相应任务ID的运行时间区间
     * @returns {Promise<void>}
     */
    const updateMissionTimeRange = async () => {
        try {
            const response = await fetch('http://127.0.0.1:5000/data_analysis/check_mission_time', {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "Authorization": "Bearer " + sessionStorage.getItem("access_token")
                },
                body: JSON.stringify({
                    radar_ID: radarID,
                    mission_ID: missionID
                })
            });

            const {status, message, data} = await response.json()
            if (response.ok) {
                setMissionTimeRange({
                    minDate: data.min_time,
                    maxDate: data.max_time
                });
            }
            console.log("获取的任务时间数据:", data);
            OpenNotification({
                type: status,
                mess: message
            })
        } catch (error) {
            console.error("获取任务时间数据失败:", error);
            OpenNotification({
                type: "error",
                mess: "获取任务时间数据失败"
            })
        }
    }

    /**
     * @method getRealTimeImage
     * @description 获取实时图片
     * @returns {Promise<void>}
     */
    const getRealTimeImage = async () => {
        try {
            const response = await fetch('http://127.0.0.1:5000/data_analysis/get_lastest_Img', {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "Authorization": "Bearer " + sessionStorage.getItem("access_token")
                },
                body: JSON.stringify({
                    radar_ID: radarID,
                    mission_ID: missionID
                })
            });
            const {status, message, data} = await response.json();
            console.log("获取实时图像数据:", {status, message, data});
            if (response.ok && status === "success") {
                viewer.addPic(data);
            }
            OpenNotification({
                type: status,
                mess: message
            })

        } catch (error) {
            console.error("获取历史图像数据失败:", error);
            OpenNotification({ type: "error", mess: "获取历史图像数据失败：" + error });
        }
    }

    /**
     * @method showHistoryImg
     * @description 获取历史图像
     * @returns {Promise<void>}
     */
    const showHistoryImg = async (values) => {
        const selectedDate = values.date_range
        if (!selectedDate || selectedDate.length < 2) {
            OpenNotification({
                type: "warning",
                mess: "时间请完整选择！"
            });
            return;
        }
        const [start, end] = selectedDate;
        const min_time = dayjs(missionTimeRange.minDate);
        const max_time = dayjs(missionTimeRange.maxDate);

        let start_time = dayjs(start);
        let end_time = dayjs(end);
        if (start_time.isBefore(min_time)) {
            start_time = min_time;
            OpenNotification({
                type: 'warning',
                mess: '选择的开始时间不得早于任务开始时间，已更换为任务开始时间!'
            })
        }
        if (end_time.isAfter(max_time)) {
            end_time = max_time;
            OpenNotification({
                type: 'warning',
                mess: '选择的结束时间不得晚于任务结束时间，已更换为任务结束时间!'
            });
        }
        if (start_time.isBefore(end_time) || start_time.isSame(end_time)) {
            const date_range = [
                start_time.format('YYYY-MM-DD HH:mm'),
                end_time.format('YYYY-MM-DD HH:mm')
            ];
            setShowDeformationRange({
                start_time: start_time.toISOString(),
                end_time: end_time.toISOString()
            })
            // 重新更新为正确时间区间的 date_range
            form.setFieldsValue({
                date_range: date_range
            });
            console.log("selectedDate", date_range)
            console.log("form date_range", form.getFieldValue("date_range"))
            OpenNotification({
                type: 'success',
                mess: '数据显示时间设置成功'
            });
        } else {
            OpenNotification({
                type: 'warning',
                mess: '选择的时间区间不合理'
            });
            return;
        }

        try {
            const response = await fetch("http://127.0.0.1:5000/data_analysis/get_history_Img", {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ' + sessionStorage.getItem("access_token")
                },
                body: JSON.stringify({
                    radar_ID: radarID,
                    mission_ID: missionID,
                    begin_time: start_time.format('YYYY-MM-DD HH:mm'),
                    end_time: end_time.format('YYYY-MM-DD HH:mm')
                })
            });
            const {status, message, data} = await response.json();
            console.log('获取的历史图像/形变信息为：', data);
            if (response.ok) {
                viewer.addPic(data);
            }
            OpenNotification({
                type: status,
                mess: message
            });
        } catch (error) {
            console.log("获取历史图像失败:", error)
            OpenNotification({
                type: "error",
                mess: "获取历史图像失败:" + error
            })
        }
    }

    useEffect(() => {
        if (missionID === null) return;
        updateMissionTimeRange()
    }, [missionID]); // 依赖 radarID，ID 变化时重新启动定时任务
    useEffect(() => {
        viewer.delePic();
        if (showTimeSelected === 1) {
            getRealTimeImage();
            setShowDeformationRange({
                begin_time: dayjs(missionTimeRange.minDate).toISOString(),
                end_time: dayjs(missionTimeRange.maxDate).toISOString()
            })
        }
    }, [showTimeSelected]);
    return (
        <Card title={"数据选择"} variant={"outlined"}>
            {/*missionList.length > 0*/}
            {missionList.length > 0 ? (
                <Form form={form}
                      onValuesChange={(changedValues, allValues) => console.log(allValues)}
                      onFinish={(values) => !values.is_real_time_data?showHistoryImg(values):undefined}
                >
                    <Space direction={"vertical"} size={"middle"}>
                        <Form.Item name={"mission_ID"} label={"任务__ID"}>
                            <Select style={{width: "70%"}}
                                    options={missionList}
                                    onChange={setMissionID}
                                    value={missionID}
                            />
                        </Form.Item>
                        <Form.Item name={"is_real_time_data"} label={"图像显示"}>
                            <Radio.Group
                                onChange={(e) => setShowTimeSelected(e.target.value)}
                                value={form.getFieldValue("is_real_time_data")}>
                                <Radio.Button value={0}>历史图像</Radio.Button>
                                <Radio.Button value={1}>实时图像</Radio.Button>
                            </Radio.Group>
                        </Form.Item>
                        <Form.Item name={"date_range"} label={showTimeSelected === 0 && "显示时间"}>
                            {showTimeSelected === 0 && (
                                <Flex aria-orientation={"horizontal"} gap={10}>
                                    <DatePicker.RangePicker
                                        placeholder={['开始时间', '结束时间']}
                                        allowEmpty={[false, true]}
                                        showTime={{format: 'HH:mm'}}
                                        format={'YYYY-MM-DD HH:mm'}
                                        // 限定只能在 minDate和maxDate 之间选一个时间段
                                        minDate={dayjs(missionTimeRange.minDate, 'YYYY-MM-DD HH:mm')}
                                        maxDate={dayjs(missionTimeRange.maxDate, 'YYYY-MM-DD HH:mm')}
                                        value = {
                                            (form.getFieldValue('date_range') || []).length > 0 ? [
                                                    dayjs(form.getFieldValue('date_range')[0], 'YYYY-MM-DD HH:mm'),
                                                    dayjs(form.getFieldValue('date_range')[1], 'YYYY-MM-DD HH:mm')] : undefined
                                        }
                                        onChange={(value, dateString) => {
                                            // console.log('NoChange Select Time:', value);
                                            console.log('NoChange Formatted Selected Time:', dateString);
                                            form.setFieldsValue({date_range: dateString});
                                        }}
                                    />
                                    {/*<Button color="primary" variant="filled" onClick={showHistoryImg}>确定</Button>*/}
                                    <Button type="primary" htmlType="submit" variant="filled">确定</Button>
                                </Flex>
                            )
                            }
                        </Form.Item>
                    </Space>
                </Form>
            ) : (
                <Space direction={"horizontal"}>
                    <SmileTwoTone twoToneColor={"#189ecc"} style={{zoom: "150%"}}/>
                    没有可用的任务列表，快去创建任务吧！
                </Space>
            )}
        </Card>
    )
}

/**\
 * @method CardShowMonitorArea
 * @description 展示检测区域
 * @param radarID
 * @param missionID
 * @param monitorArea
 * @param setMonitorArea
 * @param setActiveMonitorArea
 * @constructor
 */
const CardShowMonitorArea = ({radarID, missionID, monitorArea, setMonitorArea, setActiveMonitorArea}) => {
    const [deleteAreaName, setDeleteAreaName] = useState('');
    const [deleteOpen, setDeleteOpen] = useState(false);

    //删除图标
    const deleteMonitorAreaIcon = (Name) => (
        <DeleteOutlined
            onClick={(event) => {
                event.stopPropagation();//防止点击行为传递到父组件
                setDeleteAreaName(Name)
                setDeleteOpen(true)
            }}
        />
    )

    /**
     * @method deleteMonitorArea
     * @description 删除监测点列表
     * @returns {Promise<void>}
     */
    const deleteMonitorArea = async () => {
        try {
            const response = await fetch("http://127.0.0.1:5000/data_analysis/delete_monitor_area", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    'Authorization': 'Bearer ' + sessionStorage.getItem("access_token"),
                },
                body: JSON.stringify({ radar_ID: radarID, mission_ID: missionID, area_name: deleteAreaName }),
            });
            const {status, message} = await response.json();
            if (response.ok) {
                setDeleteOpen(false);
                await getMonitorArea();
                console.log("监测点删除成功")
            } else {
                setDeleteOpen(false);
                console.error("监测点删除失败")
            }
            OpenNotification({
                type: status,
                mess: message
            })
        } catch (error) {
            setDeleteOpen(false);
            console.log("删除检测区域失败", error);
            OpenNotification({
                type: 'error',
                mess: '删除检测区域失败'
            });
        }
    }

    //获取监测点列表
    const getMonitorArea = async () => {
        try {
            const response = await fetch("http://127.0.0.1:5000/data_analysis/check_all_monitor_area", {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ' + sessionStorage.getItem('access_token')
                },
                body: JSON.stringify({
                    radar_ID: radarID,
                    mission_ID: missionID
                })
            });
            const {status, message, data} = await response.json();
            if (response.ok) {
                const receiveData = data.map((area) => ({
                    key: area.name,
                    label: area.name,
                    children: <>
                        <Space direction={'vertical'}>
                            <Descriptions
                                title={'描述'}
                                items={[{
                                    key: 'description',
                                    children: area.description
                                }]}
                            />
                            <Descriptions
                                column={2}
                                title={'区域范围'}
                                items={[
                                    {
                                        key: 'westLon',
                                        label: '西边经度',
                                        children: area.westLon
                                    }, {
                                        key: 'eastLon',
                                        label: '东边经度',
                                        children: area.eastLon
                                    }, {
                                        key: 'southLat',
                                        label: '南边纬度',
                                        children: area.southLat
                                    }, {
                                        key: 'northLat',
                                        label: '北边纬度',
                                        children: area.northLat
                                    }
                                ]}
                            />
                        </Space>
                    </>,
                    extra: deleteMonitorAreaIcon(area.name),
                    // 下面不为Collapse所需要的，是供viewer调用
                    description: area.description,
                    westLon: area.westLon,
                    eastLon: area.eastLon,
                    southLat: area.southLat,
                    northLat: area.northLat
                }));
                setMonitorArea(receiveData);
                console.log('监测点列表加载成功');
            } else {
                console.error('监测点列表加载失败');
            }
            OpenNotification({
                type: status,
                mess: message
            })
        } catch (error) {
            console.log('获取监测点列表失败', error);
            OpenNotification({
                type: 'error',
                mess: '获取监测点列表'
            });
        }
    }
    return <>
        <Card title={"监测区域"}>
            {monitorArea.length > 0?(
                <Collapse
                    bordered={false}
                    items={monitorArea}
                    style={{backgroundColor:'#ffffff'}}
                    onChange={(key) => {
                        setActiveMonitorArea(key);
                        console.log('当前激活的监测区域名称:', key);
                    }}
                />
            ):(
                <Space direction={'horizontal'}>
                    <SmileTwoTone twoToneColor={"#189ecc"} style={{zoom: '150%'}}/>
                    没有添加监测区域，快去添加监测区域吧！
                </Space>
            )}
        </Card>
        <Modal
            title={"警告"}
            open={deleteOpen} // <-- 控制弹窗开关
            onOk={() => deleteMonitorArea()}
            onCancel={() => setDeleteOpen(false)}
        >
            确认删除：{deleteAreaName}
        </Modal>
    </>
}

import styles from './DataAnalysis.module.css'
/**
 * @method CardShowEarth
 * @description 展示地球
 * @param radarID
 * @param missionID
 * @param monitorArea
 * @param activeMonitorArea
 * @param sceneCoordinates
 * @param setSceneCoordinates
 * @constructor
 */
const CardShowEarth = ({radarID, missionID, monitorArea, activeMonitorArea, sceneCoordinates, setSceneCoordinates}) => {
    const [addAreaOpen, setAddAreaOpen] = useState(false);//添加监测区域弹窗开启状态
    const TransparentControl = ({onChange}) => {
        const [hovered, setHovered] = useState(false);      //是否触发组件展开
        const [mouseEnter, setMouseEnter] = useState(false); //判断鼠标是否进入组件范围
        const [opacity, setOpacity] = useState(1);
        return <Row
            className={styles.container}
            onMouseEnter={() => { setMouseEnter(true); console.log("鼠标进入") }}
            onMouseLeave={() => { setMouseEnter(false); console.log("鼠标退出") }}
        >
            {/*这里的div不可改为Row*/}
            <div className={`${styles.sliderWrapper} ${hovered?styles.expanded:styles.small}`}>
                {hovered?(
                    <div
                        style={{
                            backgroundColor: "rgba(255, 255, 255, 0.5)", // 半透明白色背景
                            padding: "6px 12px",
                            borderRadius: "8px",
                            backdropFilter: "blur(4px)", // 可选：玻璃拟态模糊效果
                            height: "38px",
                            display: 'flex',
                            justyContent: 'center',
                            alignItems: 'center'
                        }}
                    >
                        <Slider
                            min={0}
                            max={1}
                            step={0.01}
                            value={opacity}
                            onChange={(value) => {
                                setOpacity(value);
                                onChange?.(value); // 把 透明度数据交给viewer
                            }}
                            onChangeComplete={() => {
                                console.log('已经设置完');
                                if (mouseEnter) return;
                                setHovered(false);
                            }}
                            style={{
                                width: '100%'
                            }}
                        />
                    </div>
                ):(<Tooltip title={'调整透明度'}>
                    <Button
                        className={styles.button}
                        onClick={() => setHovered(true)} // 展开上面组件
                        icon={<DashboardOutlined/>}
                    />
                </Tooltip>)}
            </div>
        </Row>
    }
    useEffect(() => {
        console.log('sceneCoordinates', sceneCoordinates);
        viewer.build({
            lon: sceneCoordinates.lon,
            lat: sceneCoordinates.lat,
            height: sceneCoordinates.height,
            unit: 'degrees'
        });
        viewer.addPickArea(() => setAddAreaOpen(true));
        viewer.calcDist((resMsg) => {
            OpenNotification({
                type: 'seccuss',
                mess: resMsg
            })
        })
    }, []);
    return <div className={styles.earthCard}>
        <div className={styles.buttonGroup}>
                <Tooltip title={'添加观测区域'}>
                    <Button
                        className={styles.button}
                        icon={<RadiusBottomrightOutlined/>}
                        onClick={() => {viewer.areaPickFlag=true;}}
                    />
                </Tooltip>
                <Tooltip title={'显示/关闭观测区域'}>
                    <Button
                        className={styles.button}
                        icon={<PieChartOutlined/>}
                        onClick={() => {
                            viewer.monitorAreaShowFlag = !viewer.monitorAreaShowFlag;
                            console.log('当前区域显示状态为:', viewer.monitorAreaShowFlag);
                            viewer.addMonitorArea(monitorArea, activeMonitorArea);
                        }}
                    />
                </Tooltip>
                <Tooltip title={'回到地球主视角'}>
                    <Button
                        className={styles.button}
                        icon={<HomeOutlined/>}
                        onClick={() => viewer.flyTo()}
                    />
                </Tooltip>
                <Tooltip title={'回到场景主视角'}>
                    <Button
                        className={styles.button}
                        icon={<VideoCameraOutlined/>}
                        onClick={() => {
                            if (sceneCoordinates.length === 0) {
                                viewer.flyTo();
                            } else {
                                viewer.flyTo({
                                    lon: sceneCoordinates.lon,
                                    lat: sceneCoordinates.lat,
                                    height: sceneCoordinates.height,
                                    unit: 'degrees'
                                }, 3)
                            }
                        }}
                    />
                </Tooltip>
                <Tooltip title={'计算两点间距离'}>
                    <Button
                        className={styles.button}
                        icon={<BoxPlotOutlined/>}
                        onClick={() => {
                            viewer.distFlag = true;
                        }}
                    />
                </Tooltip>
                <TransparentControl // 滑条调整透明度
                    onChange={(opacity) => viewer.opacityPic(opacity)}
                />
        </div>
            <div id = 'cesiumContainer' className={styles.cesium}/>
    </div>
}
/**
 * @method CardDeformationLine
 * @description 形变展示卡片
 */
const CardDeformationLine = () => {
    /**
     * @method getDeformationData
     * @description 获取形变数据
     * @returns {Promise<void>}
     */
    const getDeformationData = async ({radarID, missionID, activeMonitorArea, showDeformationRange }) => {
        try {
            const response = await fetch('http://127.0.0.1:5000/data_analysis/get_monitor_area_deformation', {
                method: 'POST',
                headers: {
                    "Content-Type": 'application',
                    'Authorization': 'Bearer ' + sessionStorage.getItem('access_token')
                },
                body: JSON.stringify({
                    radar_ID: radarID,
                    mission_ID: missionID,
                    area_list: activeMonitorArea,
                    begin_time: showDeformationRange["begin_time"],
                    end_time: showDeformationRange["end_time"]})
            });
            const {status, message, data} = await response.json();
            if (response.ok) {
                console.log(data)
            }
        } catch (error) {
            console.log("获取形变数据失败", error);
            OpenNotification({
                type: 'error',
                mess: "获取形变数据失败" + error
            })
        }
    }
}