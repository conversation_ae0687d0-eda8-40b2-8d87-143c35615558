.earthCard{
    width: 100%;
    height: 100%;
    position: relative;
}

.earthCard .buttonGroup{
    z-index: 100;
    position: absolute;
    background: transparent; /* 设置背景透明 */
    top: 0;
    left: 0;
}

.earthCard .buttonGroup .button{
    zoom: 120%;
    background: rgba(255, 255, 255, 0.5);   /*白色半透明*/
    border: none;                           /*可选：去掉边框*/
    boxShadow: none;                        /*可选：去掉阴影*/
}

.earthCard .cesium{
    z-index: 0;
    position: absolute;
    height: 100%;
    top: 0;
    left: 0;
    border: aqua solid 3px;
}

.container {
    display: inline-block;
    position: absolute;
    top: 0;
}

.sliderWrapper {
    width: 40px;
    transition: all 0.4s ease;
    opacity: 1;
}

.sliderWrapper.expanded {
    width: 160px;
    transition: all 0.4s ease;
    opacity: 1;
}
.sliderWrapper.small {
    width: 40px;
    transition: all 0.4s ease;
    opacity: 1;
}