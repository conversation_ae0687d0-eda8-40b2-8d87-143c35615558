import { useEffect, useState, useRef } from "react";
import { NotLoggedIn, OpenNotification } from "../notification";
import { Button, Descriptions, Layout, Typography, Row, Col } from "antd";

const { Content, Footer } = Layout;

export function RadarInformation({
    radarID,
    loginState,
    radarInformation,
    setRadarInformation,
    sceneName,
}) {
    const [loading, setLoading] = useState(false);
    const enterLoading = async () => {
        if (!radarID) {
            OpenNotification({
                type: "warning",
                mess: "请先选择雷达"
            })
        } else {
            try {
                setLoading(true); // 开始加载
                const response = await fetch("http://127.0.0.1:5000/radar_information/update_radar_information", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "Authorization": "Bearer " + sessionStorage.getItem("access_token"),
                    },
                    body: JSON.stringify({ radar_ID: radarID })
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    // console.error("获取雷达信息失败:", errorData);
                    OpenNotification({ type: "error", mess: `获取雷达信息失败: ${errorData.message || '未知错误'}` });
                    return;
                }

                const {status, message, data} = await response.json();
                const generatedItems = data.map(item => ({
                    key: item.key,
                    label: item.label,
                    children: item.value ?? "--", // 使用服务器返回的值
                }));
                setRadarInformation(generatedItems);
                // console.log("雷达信息请求成功:", data);
                OpenNotification({
                    type: status,
                    mess: message
                });
            } catch (error) {
                // console.error("雷达信息请求失败:", error);
                OpenNotification({
                    type: "error",
                    mess: "雷达信息请求失败" + error
                });
            } finally {
                setTimeout(() => {
                    setLoading(false)
                }, 5000);
            }
        }
    }
    // 使用 useRef 来跟踪 enterLoading 是否已被调用
    const isLoadingCalledRef = useRef(false);

    useEffect(() => {
        if (loginState === false) {
            return;
        }
        // 只有在第一次渲染时调用 enterLoading
        if (!isLoadingCalledRef.current) {
            enterLoading();
            isLoadingCalledRef.current = true;
        }
    }, [])
    // console.log("场景---:", sceneName)
    const styleObj = {
        label: { width: "16.6%" },
        content: { width: "16.6%" }
    }
    return (
        <Row justify={"center"} gutter={[25, 20]}>
            <Col span={24}>
                <Typography.Title level={4}>整机信息</Typography.Title>
                <Descriptions bordered items={radarInformation.slice(0, 6)} column={3}
                    styles={styleObj}
                />
            </Col>
            <Col span={24}>
                <Typography.Title level={4}>雷达信息</Typography.Title>
                <Descriptions bordered items={radarInformation.slice(6, 37)} column={3}
                    styles={styleObj}
                />
            </Col>
            <Col span={24}>
                <Typography.Title level={4}>电控箱信息</Typography.Title>
                <Descriptions bordered items={radarInformation.slice(37, 49)} column={3}
                    styles={styleObj}
                />
            </Col>
            {/*<Col push={24}>*/}
            <Button type="primary" loading={loading} onClick={() => enterLoading()}>
                查询雷达信息
            </Button>
            {/*</Col>*/}
        </Row>
    )

}