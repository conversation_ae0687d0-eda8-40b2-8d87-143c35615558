import React, {useEffect} from "react";
import {Navigate, Outlet} from "react-router-dom";
import {HomeHead} from "./HomeHead.jsx";
import {Layout} from "antd";

const {Footer, Content} = Layout;
export default function PrivateRoute({
                                         radarID,
                                         setRadarID,
                                         radarList,
                                         setRadarList,
                                         radarState,
                                         setRadarState,
                                         loginState,
                                         setLoginState,
                                         setSceneCoordinates,
                                         sceneName
                                     }) {
    const isLoggedIn = sessionStorage.getItem("access_token") != null;
    useEffect(() => {
        if (isLoggedIn) {
            setLoginState(true);
            console.log("登录成功");
            console.log("PrivateRoute记录的登陆状态:", loginState);
            console.log("PrivateRoute记录的场景名称:", sceneName);
            console.log("PrivateRoute记录的雷达列表:", radarList);
            const storedRadarList = JSON.parse(sessionStorage.getItem("radar_list") || "[]");
            console.log("PrivateRoute记录的会话雷达列表为：",storedRadarList);
            // // const options = storedRadarList.map((radar) => ({
            // //     value: radar.value,    // 这里假设服务器返回的数据包含 id
            // //     label: radar.label,  // 这里假设服务器返回的数据包含 name
            // // }));
            // setRadarList(storedRadarList);
        }
    }, []);
    // useEffect(() => {
    //     console.log("测试，修改当前的列表为：",radarList);
    // }, [radarList]);

    return isLoggedIn ? (
        <Layout>
            <HomeHead
                radarID={radarID}
                setRadarID={setRadarID}
                radarList={radarList}
                setRadarList={setRadarList}
                radarState={radarState}
                setRadarState={setRadarState}
                loginState={loginState}
                setLoginState={setLoginState}
                setSceneCoordinates={setSceneCoordinates}
                sceneName={sceneName}
                activeNumber="2"
            />
            <Content style={{ padding: '24px', minHeight: '280px', backgroundColor: '#fff' }}>
                <Outlet/>
            </Content>
            <Footer style={{fontSize: 20, textAlign: 'center'}}>
                边坡形变监测系统
            </Footer>
        </Layout>
    ) : <Navigate to="/test_login"/>;
}
