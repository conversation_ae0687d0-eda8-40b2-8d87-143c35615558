import * as Cesium from "cesium"
import {OpenNotification} from "../notification.jsx";

export class CesiumViewer {
    geoInfoBox;
    areaPickFlag = false;           //选取区域标志
    distFlag = false;               //计算距离标志
    startPoint = null;
    endPoint = null;
    currPoint = null;
    currentAreaEntity = null;      //当前选择区域实体
    monitorAreaEntities = []; // 存储所有监测
    monitorAreaShowFlag = true;//是否显示监测区域
    radarHost = null;          //雷达图标实体
    pickOnePoint = false;       //点击获取单个坐标
    CesiumViewerID;     //区分不同ID
    PicLayer = null;           //图片贴图图层
    terrainProvider;    //DEM数据地址
    rotateEnabled = false;      //旋转效果标志
    viewer:Cesium.Viewer = null;
    // 优化：添加事件监听器引用，便于清理
    postUpdateHandler = null;

    // 构造viewer
    constructor(containerId) {
        this.CesiumViewerID = containerId;
    }

    // 根据场景坐标创建地球
    async build_login() {
        this.rotateEnabled = true;
        // https://cesium.com/learn/ion-sdk/ref-doc/Viewer.html
        if (!this.viewer || this.viewer.isDestroyed()){
            console.log("create earth");
            this.viewer = new Cesium.Viewer(this.CesiumViewerID, {
                infoBox: false,   // 关闭信息框，解决报错
                // geocoder: false,  // 关闭右上角搜索输入
                homeButton: false,  // 关闭右上角home按钮，功能是回到默认视场
                sceneModePicker: false,   // 关闭右上角2D/3D选择按钮
                baseLayerPicker: false,   // 关闭图层选择
                navigationHelpButton: false,  // 关闭右上角帮助按钮
                animation: false,   // 关闭动画，左下角的圆形控件
                timeline: false,  // 关闭时间线
                fullscreenButton: false,   // 去掉右下角的全屏按钮
                geocoder: false,           //右上角的查询按钮，可以根据输入的地名地址，定位到查询结果
                requestRenderMode: true,   // 仅在需要时渲染场景
                maximumRenderTimeChange: Infinity, // 无论场景如何变化都渲染
            })
            // 去除cesium logo水印
            // 老版本方法 this.viewer.cesiumWidget.creditContainer.style.display = "none";
            // https://stackoverflow.com/questions/76015471/how-can-i-prevent-the-cesium-ion-logo-from-showing-up
            this.viewer.cesiumWidget.creditContainer.parentNode.removeChild(this.viewer.cesiumWidget.creditContainer);

            // let initialOrientation = Cesium.HeadingPitchRoll.fromDegrees(270, -70, 90);
            // console.log({
            //     pitch: Cesium.Math.toRadians(-70),   // -90 是正下，-30 是倾斜看
            //     roll: Cesium.Math.toRadians(90),
            //     heading: Cesium.Math.toRadians(270),
            // })
            // console.log({
            //     pitch: initialOrientation.pitch,
            //     roll: initialOrientation.roll,
            //     heading: initialOrientation.heading,
            // })
            this.viewer.camera.setView({
                destination: Cesium.Cartesian3.fromDegrees(40, 17, 7000000), // 起点的经纬度和高度
                orientation: Cesium.HeadingPitchRoll.fromDegrees(270, -70, 90)
            });

            // 方法1
            if (this.postUpdateHandler) {
                this.viewer.scene.postUpdate.removeEventListener(this.postUpdateHandler);
            }

            this.postUpdateHandler = this.viewer.scene.postUpdate.addEventListener(() => {
                if (this.rotateEnabled) {
                    this.viewer.scene.camera.rotate(Cesium.Cartesian3.UNIT_Z, -0.0005);
                }
            });

        }

    }

    /**
     * 调整相机视角到指定点
     * @param {Array} point - 目标点坐标数组，格式为[经度, 纬度, 高度]，默认值为[113.950, 22.805, 5000000]
     * @param {number} flyTime - 飞行动画持续时间(秒)，默认值为5秒
     */
    flyTo(point = [113.950, 22.805, 5000000], flyTime = 5){
        const [lon, lat, height] = point;
        this.viewer.camera.flyTo({
            destination: Cesium.Cartesian3.fromDegrees(lon, lat, height),
            duration: flyTime
        })
    }

    // 销毁资源
    destroy(){
        this.areaPickFlag = false;
        this.distFlag = false;
        this.startPoint = null;
        this.endPoint = null;
        this.currPoint = null;
        this.currentAreaEntity = null;
        this.monitorAreaShowFlag = true;
        this.pickOnePoint = false;
        this.rotateEnabled = false;

        //删除所有矩形
        this.monitorAreaEntities = [];
        this.radarHost = null;
        if (this.viewer !== null) {
            this.viewer.destroy();
        }
        this.PicLayer = null;
        this.terrainProvider = null;
        console.log("地球销毁");
    }

}